/**
 * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially useful
 * for Docker builds.
 */
import "./src/env.js";

/** @type {import("next").NextConfig} */
const config = {
  experimental: {
    authInterrupts: true,
  },
  async redirects() {
    return [
      {
        source: "/",
        destination: "/organizations",
        permanent: false,
      },
    ];
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "picsum.photos",
      },
      {
        protocol: "https",
        hostname: "psa-poc-bucket.fly.storage.tigris.dev",
      },
      {
        protocol: "https",
        hostname: "psa-poc-bucket.t3.storage.dev",
      },
      {
        protocol: "https",
        hostname: "slydeshow-dev.t3.storage.dev",
      },
      {
        protocol: "https",
        hostname: "slydeshow-prod.t3.storage.dev",
      },
      {
        protocol: "https",
        hostname: "storage.slydeshow.com",
      },
    ],
  },
  devIndicators: {
    position: "bottom-right",
  },
};

export default config;
