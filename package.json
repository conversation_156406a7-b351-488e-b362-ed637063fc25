{"name": "slydeshow", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build --turbo", "check": "next lint && tsc --noEmit", "better-auth:generate": "pnpx @better-auth/cli generate --config ./src/libs/auth.ts", "db:generate": "prisma generate --sql", "db:migrate": "prisma migrate deploy", "db:push": "prisma db push", "db:studio": "prisma studio", "dev": "next dev --turbo", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "postinstall": "prisma generate --sql", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit", "preview-emails": "email dev --dir ./src/emails/templates --port 3333", "stripe:listen": "stripe listen --forward-to localhost:3000/api/auth/stripe/webhook"}, "dependencies": {"@aws-sdk/client-s3": "^3.844.0", "@aws-sdk/s3-request-presigner": "^3.844.0", "@better-auth/stripe": "^1.2.12", "@better-fetch/fetch": "^1.1.17", "@dnd-kit/helpers": "^0.1.19", "@dnd-kit/react": "^0.1.19", "@hookform/resolvers": "^5.1.1", "@prisma/adapter-planetscale": "^6.11.1", "@prisma/client": "^6.11.1", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-visually-hidden": "^1.2.3", "@react-email/components": "^0.3.2", "@react-email/tailwind": "^1.2.2", "@t3-oss/env-nextjs": "^0.13.8", "@tabler/icons-react": "^3.34.0", "@tanstack/react-query": "^5.83.0", "@tanstack/react-table": "^8.21.3", "@trpc/client": "^11.4.3", "@trpc/react-query": "^11.4.3", "@trpc/server": "^11.4.3", "better-auth": "^1.2.12", "better-upload": "^0.2.7", "browser-image-compression": "^2.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "fractional-indexing": "^3.2.0", "geist": "^1.4.2", "lucide-react": "^0.525.0", "nanoid": "^5.0.9", "next": "^15.4.1", "nextjs-toploader": "^3.8.16", "react": "^19.1.0", "react-day-picker": "^9.8.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-email": "^4.2.4", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.60.0", "react-intersection-observer": "^9.16.0", "resend": "^4.6.0", "server-only": "^0.0.1", "sharp": "^0.34.3", "sonner": "^2.0.6", "stripe": "^18.3.0", "superjson": "^2.2.1", "tailwind-merge": "^3.3.1", "ua-parser-js": "^2.0.4", "vaul": "^1.1.2", "zod": "^3.25.67", "zod-error": "^1.5.0", "zod-openapi": "^4.2.4", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@react-email/preview-server": "4.2.4", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@types/eslint": "^9.6.1", "@types/node": "^24.0.14", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "eslint": "^9.31.0", "eslint-config-next": "^15.4.1", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "prisma": "^6.11.1", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3"}, "ct3aMetadata": {"initVersion": "7.38.1"}}