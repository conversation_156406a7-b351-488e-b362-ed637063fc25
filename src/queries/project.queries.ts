import { useOrganizationSlug } from "@/hooks/use-organization-slug";
import { api } from "@/trpc/react";
import type { ProjectsFindInput, ProjectsOutput } from "@/types/project.types";
import { isEmpty } from "@/utils/is-empty";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

export const useProjectById = (id: string) => {
  return api.projects.getById.useQuery({ id: id }, { enabled: !isEmpty(id) });
};

export function useProjects(organizationId: string | undefined) {
  return api.projects.getAll.useQuery(
    { organizationId: organizationId ?? "" },
    { enabled: !isEmpty(organizationId) },
  );
}

export function useInfiniteProjects(
  input?: ProjectsFindInput,
  initialData?: ProjectsOutput,
) {
  return api.projects.getAll.useInfiniteQuery(
    { ...input, organizationId: input?.organizationId ?? "" },
    {
      enabled: !isEmpty(input?.organizationId),
      initialData: () => {
        if (initialData) {
          return {
            pageParams: [undefined],
            pages: [initialData],
          };
        }
      },
      getNextPageParam: (lastPage) => lastPage?.cursor || undefined,
    },
  );
}

export function useProjectAddMutation() {
  const slug = useOrganizationSlug();
  const router = useRouter();
  const utils = api.useUtils();

  return api.projects.create.useMutation({
    onSuccess: async (data) => {
      router.push(`/${slug}/projects/${data.id}`);
    },
    onError: (error, _input, _ctx) => {
      console.log(error);
      toast.error("Error", { description: error.message });
    },
    onSettled: async () => {
      await utils.projects.getAll.invalidate();
      await utils.projects.getAll.invalidate();
      await utils.projects.getAllActive.invalidate();
      await utils.projects.getActivityStats.invalidate();
      await utils.approvals.invalidate();
    },
  });
}

export const useProjectUpdateMutation = () => {
  const utils = api.useUtils();

  return api.projects.updateById.useMutation({
    onError: (error, _input, _ctx) => {
      console.log(error);
      toast.error("Error", { description: error.message });
    },
    onSettled: async (_data, _error, input) => {
      await utils.projects.getById.invalidate({
        id: input.id,
      });
      await utils.projects.getAll.invalidate();
      await utils.projects.getAllActive.invalidate();
      await utils.projects.getActivityStats.invalidate();
      await utils.approvals.invalidate();
    },
  });
};

export const useProjectPublishMutation = () => {
  const utils = api.useUtils();

  return api.projects.publishProject.useMutation({
    onError: (error, _input, _ctx) => {
      console.log(error);
      toast.error("Error", { description: error.message });
    },
    onSettled: async (data, _error) => {
      await utils.projects.getById.invalidate({ id: data?.id });
      await utils.projects.getAll.invalidate();
      await utils.projects.getAllActive.invalidate();
      await utils.projects.getActivityStats.invalidate();
    },
  });
};

export const useProjectDeleteMutation = () => {
  const utils = api.useUtils();

  return api.projects.deleteById.useMutation({
    onError: (error, _input, _ctx) => {
      console.log(error);
      toast.error("Error", { description: error.message });
    },
    onSettled: async (_data, _error, input) => {
      await utils.projects.getById.invalidate({ id: input.id });
      await utils.projects.getAll.invalidate();
      await utils.projects.getAllActive.invalidate();
      await utils.projects.getActivityStats.invalidate();
    },
  });
};

export const useProjectViewDeleteMutation = () => {
  const slug = useOrganizationSlug();
  const router = useRouter();
  const utils = api.useUtils();

  return api.projects.deleteById.useMutation({
    onSuccess: async (_data) => {
      router.push(`/${slug}/projects`);
    },
    onError: (error, _input, _ctx) => {
      console.log(error);
      toast.error("Error", { description: error.message });
    },
    onSettled: async (_data, _error, _input) => {
      await utils.projects.getAll.invalidate();
      await utils.projects.getAllActive.invalidate();
      await utils.projects.getActivityStats.invalidate();
    },
  });
};
