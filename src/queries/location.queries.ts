import { api } from "@/trpc/react";
import type { LocationsFindInput } from "@/types/location.types";
import { isEmpty } from "@/utils/is-empty";
import { toast } from "sonner";

interface LocationsInput {
  organizationId: string;
  searchString?: string;
  take?: number;
}

// Location query hooks
export function useLocations(input: LocationsInput) {
  return api.locations.getAll.useQuery(
    {
      organizationId: input.organizationId,
      searchString: input.searchString,
      take: input.take,
    },
    { enabled: !isEmpty(input.organizationId) },
  );
}

export function useInfiniteLocations(input?: LocationsFindInput) {
  return api.locations.getAll.useInfiniteQuery(
    { ...input, organizationId: input?.organizationId ?? "" },
    {
      enabled: !isEmpty(input?.organizationId),

      getNextPageParam: (lastPage) => lastPage?.cursor || undefined,
    },
  );
}

export function useLocationById(id: string) {
  return api.locations.getById.useQuery({ id }, { enabled: !isEmpty(id) });
}

export function useLocationCreateMutation() {
  const utils = api.useUtils();

  return api.locations.create.useMutation({
    onSuccess: async () => {
      toast.success("Location created successfully");
    },
    onError: (error) => {
      console.log(error);
      toast.error("Something went wrong!", {
        description: "An error occurred while creating the location.",
        closeButton: true,
      });
    },
    onSettled: async () => {
      await utils.locations.getAll.invalidate();
      await utils.projects.getAllActive.invalidate();
      await utils.projects.getActivityStats.invalidate();
    },
  });
}

export function useLocationUpdateMutation() {
  const utils = api.useUtils();

  return api.locations.updateById.useMutation({
    onSuccess: async () => {
      toast.success("Location updated successfully");
    },
    onError: (error) => {
      console.log(error);
      toast.error("Something went wrong!", {
        description: "An error occurred while updating the location.",
        closeButton: true,
      });
    },
    onSettled: async () => {
      await utils.locations.getAll.invalidate();
      await utils.projects.getAllActive.invalidate();
      await utils.projects.getActivityStats.invalidate();
    },
  });
}

export function useLocationDeleteMutation() {
  const utils = api.useUtils();

  return api.locations.deleteById.useMutation({
    onSuccess: async () => {
      toast.success("Location deleted successfully");
    },
    onError: (error) => {
      console.log(error);
      toast.error("Something went wrong!", {
        description: "An error occurred while deleting the location.",
        closeButton: true,
      });
    },
    onSettled: async () => {
      await utils.locations.getAll.invalidate();
    },
  });
}

// SubLocation query hooks
export function useSubLocationsByLocationId(locationId: string) {
  return api.locations.getSubLocationsByLocationId.useQuery(
    { locationId },
    { enabled: !isEmpty(locationId) },
  );
}

export function useSubLocationById(id: string) {
  return api.locations.getSubLocationById.useQuery(
    { id },
    { enabled: !isEmpty(id) },
  );
}

export function useSubLocationCreateMutation() {
  const utils = api.useUtils();

  return api.locations.createSubLocation.useMutation({
    onSuccess: async () => {
      toast.success("Sublocation created successfully");
    },
    onError: (error) => {
      console.log(error);
      toast.error("Something went wrong!", {
        description: "An error occurred while creating the sublocation.",
        closeButton: true,
      });
    },
    onSettled: async () => {
      await utils.locations.getAll.invalidate();
      await utils.projects.getAllActive.invalidate();
      await utils.projects.getActivityStats.invalidate();
    },
  });
}

export function useSubLocationUpdateMutation() {
  const utils = api.useUtils();

  return api.locations.updateSubLocationById.useMutation({
    onSuccess: async () => {
      toast.success("Sublocation updated successfully");
    },
    onError: (error) => {
      console.log(error);
      toast.error("Something went wrong!", {
        description: "An error occurred while updating the sublocation.",
        closeButton: true,
      });
    },
    onSettled: async () => {
      await utils.locations.getAll.invalidate();
      await utils.projects.getAllActive.invalidate();
      await utils.projects.getActivityStats.invalidate();
    },
  });
}

export function useSubLocationDeleteMutation() {
  const utils = api.useUtils();

  return api.locations.deleteSubLocationById.useMutation({
    onSuccess: async (_, _variables) => {
      toast.success("Sublocation deleted successfully");
    },
    onError: (error) => {
      console.log(error);
      toast.error("Something went wrong!", {
        description: "An error occurred while deleting the sublocation.",
        closeButton: true,
      });
    },
    onSettled: async (_, __, variables) => {
      await utils.locations.getAll.invalidate();
      await utils.projects.getAllActive.invalidate();
      await utils.projects.getActivityStats.invalidate();
    },
  });
}
