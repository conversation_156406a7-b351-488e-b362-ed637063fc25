import { useCallback, useEffect, useState } from "react";
import { useWindow } from "./use-window";

/**
 * A custom React hook for persistent state management using localStorage.
 * Handles SSR safely and provides type-safe access to stored values.
 *
 * @template T The type of the stored value
 * @param {string} key The key under which the value will be stored in localStorage
 * @param {T} initialValue The initial value to use if no value is stored
 * @returns {[T, (value: T | ((val: T) => T)) => void, () => void]} A tuple containing:
 * - The current stored value
 * - A function to update the stored value
 * - A function to remove the stored value
 *
 * @example
 * ```tsx
 * function ThemeToggle() {
 *   const [theme, setTheme, removeTheme] = useLocalStorage('theme', 'light');
 *
 *   const toggleTheme = () => {
 *     setTheme(current => current === 'light' ? 'dark' : 'light');
 *   };
 *
 *   return (
 *     <button onClick={toggleTheme}>
 *       Current theme: {theme}
 *     </button>
 *   );
 * }
 * ```
 */
export function useLocalStorage<T = string>(
  key: string,
  initialValue: T = "" as T,
): [T, (value: T | ((val: T) => T)) => void, () => void] {
  const window = useWindow();
  const [storedValue, setStoredValue] = useState<T>(initialValue);

  // Initialize the state
  useEffect(() => {
    try {
      if (window?.localStorage) {
        const item = window.localStorage.getItem(key);
        setStoredValue(item ? JSON.parse(item) : initialValue);
      } else {
        setStoredValue(initialValue);
      }
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
      setStoredValue(initialValue);
    }
  }, [window, key, initialValue]);

  // Return a wrapped version of localStorage's setItem function
  const setValue = useCallback(
    (value: T | ((val: T) => T)) => {
      try {
        // Allow value to be a function for setter-style updates
        const valueToStore =
          value instanceof Function ? value(storedValue) : value;

        setStoredValue(valueToStore);

        if (window?.localStorage) {
          window.localStorage.setItem(key, JSON.stringify(valueToStore));
        }
      } catch (error) {
        console.warn(`Error setting localStorage key "${key}":`, error);
      }
    },
    [window, key, storedValue],
  );

  const removeValue = useCallback(() => {
    try {
      if (window?.localStorage) {
        window.localStorage.removeItem(key);
      }
      setStoredValue(initialValue);
    } catch (error) {
      console.warn(`Error removing localStorage key "${key}":`, error);
    }
  }, [window, key, initialValue]);

  return [storedValue, setValue, removeValue];
}

/**
 * A custom React hook that provides a function to set localStorage values.
 * Useful when you only need to write to localStorage without reading or managing state.
 *
 * @returns {<T>(key: string, value: T) => void} A function to set localStorage values
 *
 * @example
 * ```tsx
 * function SaveButton() {
 *   const setToStorage = useSetLocalStorage();
 *
 *   const handleSave = () => {
 *     setToStorage('user-settings', { theme: 'dark', fontSize: 14 });
 *   };
 *
 *   return <button onClick={handleSave}>Save Settings</button>;
 * }
 * ```
 */
export function useSetLocalStorage(): <T>(key: string, value: T) => void {
  const window = useWindow();

  return useCallback(
    <T>(key: string, value: T) => {
      try {
        if (window?.localStorage) {
          window.localStorage.setItem(key, JSON.stringify(value));
        }
      } catch (error) {
        console.warn(`Error setting localStorage key "${key}":`, error);
      }
    },
    [window],
  );
}
