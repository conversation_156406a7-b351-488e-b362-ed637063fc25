import { useOrganizationMemberRole } from "@/queries/organization.queries";
import { Roles } from "@/types/organization.types";

export const usePermissions = () => {
  const userRole = useOrganizationMemberRole();

  return {
    canPublishProject: userRole === Roles.ADMIN,
    canEditProject: [Roles.ADMIN, Roles.MANAGER].includes(userRole as any),
    canViewProject: [Roles.ADMIN, Roles.MANAGER, Roles.MEMBER].includes(
      userRole as any,
    ),
    canManageMembers: userRole === Roles.ADMIN,
    canViewReports: [Roles.ADMIN, Roles.MANAGER].includes(userRole as any),
    isAdmin: userRole === Roles.ADMIN,
    isManager: userRole === Roles.MANAGER,
    isMember: userRole === Roles.MEMBER,
  };
};
