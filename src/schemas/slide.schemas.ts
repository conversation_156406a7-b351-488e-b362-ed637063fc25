import { z } from "@/libs/zod";

export const SlideCreateSchema = z.object({
  projectId: z.string(),
  imageUrl: z.string(),
  objectKey: z.string(),
  order: z.string(),
});

export const SlideCreateManySchema = z.object({
  projectId: z.string(),
  lastSlideId: z.string(),
  images: z.array(z.object({ imageUrl: z.string(), objectKey: z.string() })),
});

export const SlideUpdateSchema = SlideCreateSchema.partial().extend({
  duration: z.number().nullable().optional(),
});
