import { type RouterInputs, type RouterOutputs } from "@/trpc/react";
import type { InfiniteData } from "@tanstack/react-query";

// Location Types
export type LocationCreateData = RouterInputs["locations"]["create"];
export type LocationUpdateData = Omit<
  RouterInputs["locations"]["updateById"],
  "id"
>;

export type LocationsOutput = RouterOutputs["locations"]["getAll"];
export type LocationOutput = RouterOutputs["locations"]["getById"];

export type LocationsFindInput = RouterInputs["locations"]["getAll"];

export type InfiniteLocationsData = InfiniteData<LocationsOutput> | undefined;

// SubLocation Types
export type SubLocationCreateData =
  RouterInputs["locations"]["createSubLocation"];
export type SubLocationUpdateData = Omit<
  RouterInputs["locations"]["updateSubLocationById"],
  "id"
>;

export type SubLocationsOutput =
  RouterOutputs["locations"]["getSubLocationsByLocationId"];
export type SubLocationOutput =
  RouterOutputs["locations"]["getSubLocationById"];

// Legacy interfaces for backward compatibility
export interface Location {
  id: string;
  name: string;
  address: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
  organizationId: string;
  createdAt: Date;
  updatedAt: Date;
  sublocations?: SubLocation[];
  projects?: Array<{ id: string; name: string; status: string }>;
  _count?: { projects: number };
}

export interface SubLocation {
  id: string;
  name: string;
  locationId: string;
  createdAt: Date;
  updatedAt: Date;
  location?: Location;
}

export interface LocationCreateInput {
  name: string;
  address: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
  organizationId: string;
}

export interface LocationUpdateInput extends Partial<LocationCreateInput> {
  id: string;
}

export interface SubLocationCreateInput {
  name: string;
  locationId: string;
}

export interface SubLocationUpdateInput
  extends Partial<SubLocationCreateInput> {
  id: string;
}
