import type { RouterInputs, RouterOutputs } from "@/trpc/react";
import type { InfiniteData } from "@tanstack/react-query";

export type SlideCreateInput = RouterInputs["slides"]["create"];

export type SlideUpdateInput = RouterInputs["slides"]["updateById"];

export type SlidesFindInput = RouterInputs["slides"]["getAll"];

export type Slide = RouterOutputs["slides"]["getById"];

export type SlidesOutput = RouterOutputs["slides"]["getAll"];

export type SlideshowSlides = RouterOutputs["slides"]["getForLocation"];

export type InfiniteSlidesData = InfiniteData<SlidesOutput>;

export type SlideshowPreviewSlide = Pick<
  Slide,
  "id" | "imageUrl" | "order" | "duration"
>;
