import { type RouterInputs, type RouterOutputs } from "@/trpc/react";
import type { InfiniteData } from "@tanstack/react-query";

export type OrganizationCreateData = RouterInputs["organizations"]["create"];
export type OrganizationUpdateData = Omit<
  RouterInputs["organizations"]["updateById"],
  "id"
>;

export type OrganizationsOutput = RouterOutputs["organizations"]["getAll"];
export type OrganizationOutput = RouterOutputs["organizations"]["getById"];

export type OrganizationFindInput = RouterInputs["organizations"]["getAll"];

export type OrganizationInvite =
  RouterOutputs["organizations"]["getInvites"][0];

export type OrganizationMember =
  RouterOutputs["organizations"]["getMembers"][0];

export type InfiniteOrganizationsData =
  | InfiniteData<OrganizationsOutput>
  | undefined;

export const Roles = {
  ADMIN: "admin",
  MANAGER: "manager",
  MEMBER: "member",
} as const;

export type Role = (typeof Roles)[keyof typeof Roles];
