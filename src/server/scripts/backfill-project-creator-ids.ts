import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function backfillProjectCreatorIds() {
  console.log("🚀 Starting project creator IDs backfill...");

  try {
    // Get all projects without creators
    const projectsWithoutCreators = await prisma.project.findMany({
      where: {
        creatorId: null,
      },
      include: {
        organization: {
          include: {
            members: {
              where: {
                role: "admin",
              },
              orderBy: {
                createdAt: "asc", // Get the oldest admin (likely the organization creator)
              },
              take: 1,
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    console.log(
      `📊 Found ${projectsWithoutCreators.length} projects without creators`,
    );

    if (projectsWithoutCreators.length === 0) {
      console.log("✅ All projects already have creators!");
      return;
    }

    let updatedCount = 0;
    let skippedCount = 0;

    for (const project of projectsWithoutCreators) {
      const firstAdmin = project.organization?.members[0];

      if (firstAdmin) {
        try {
          await prisma.project.update({
            where: { id: project.id },
            data: { creatorId: firstAdmin.userId },
          });

          console.log(
            `✅ Set creator for project "${project.name}" to ${firstAdmin.user.name} (${firstAdmin.user.email})`,
          );
          updatedCount++;
        } catch (error) {
          console.error(
            `❌ Failed to update project "${project.name}":`,
            error,
          );
          skippedCount++;
        }
      } else {
        console.warn(
          `⚠️  No admin found for organization of project "${project.name}" - skipping`,
        );
        skippedCount++;
      }
    }

    console.log("\n📈 Backfill Summary:");
    console.log(`   ✅ Updated: ${updatedCount} projects`);
    console.log(`   ⚠️  Skipped: ${skippedCount} projects`);
    console.log(
      `   📊 Total processed: ${projectsWithoutCreators.length} projects`,
    );

    // Verify the results
    const remainingProjectsWithoutCreators = await prisma.project.count({
      where: {
        creatorId: null,
      },
    });

    console.log(
      `\n🔍 Verification: ${remainingProjectsWithoutCreators} projects still without creators`,
    );

    if (remainingProjectsWithoutCreators === 0) {
      console.log(
        "🎉 Backfill completed successfully! All projects now have creators.",
      );
    } else {
      console.log("⚠️  Some projects still need manual attention.");
    }
  } catch (error) {
    console.error("💥 Error during backfill:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Alternative function to assign based on project approval history
async function backfillBasedOnApprovalHistory() {
  console.log("🔄 Alternative: Backfilling based on approval history...");

  try {
    const projectsWithoutCreators = await prisma.project.findMany({
      where: {
        creatorId: null,
      },
      include: {
        approvals: {
          orderBy: {
            createdAt: "asc",
          },
          take: 1,
          include: {
            requester: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
      },
    });

    let updatedFromApprovals = 0;

    for (const project of projectsWithoutCreators) {
      const firstApproval = project.approvals[0];

      if (firstApproval) {
        try {
          await prisma.project.update({
            where: { id: project.id },
            data: { creatorId: firstApproval.requesterId },
          });

          console.log(
            `✅ Set creator for project "${project.name}" to ${firstApproval.requester.name} (from approval history)`,
          );
          updatedFromApprovals++;
        } catch (error) {
          console.error(
            `❌ Failed to update project "${project.name}":`,
            error,
          );
        }
      }
    }

    console.log(
      `📊 Updated ${updatedFromApprovals} projects from approval history`,
    );
  } catch (error) {
    console.error("💥 Error during approval-based backfill:", error);
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const useApprovalHistory = args.includes("--approval-history");

  if (useApprovalHistory) {
    await backfillBasedOnApprovalHistory();
  } else {
    await backfillProjectCreatorIds();
  }
}

main().catch((error) => {
  console.error("💥 Script failed:", error);
  process.exit(1);
});
