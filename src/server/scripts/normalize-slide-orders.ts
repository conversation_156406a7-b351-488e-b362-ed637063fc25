import { db } from "@/server/db";
import { generateKeyBetween } from "fractional-indexing";

export async function normalizeSlideOrders() {
  // Get all projects
  const projects = await db.project.findMany({
    select: { id: true },
  });

  for (const project of projects) {
    // Get all slides for this project
    const slides = await db.slide.findMany({
      where: { projectId: project.id },
      orderBy: { order: "asc" },
    });

    // Normalize the order values
    let prevOrder = null;
    const updates = [];

    for (const slide of slides) {
      const newOrder = generateKeyBetween(prevOrder, null);
      updates.push(
        db.slide.update({
          where: { id: slide.id },
          data: { order: newOrder },
        }),
      );
      prevOrder = newOrder;
    }

    // Execute all updates in a transaction
    if (updates.length > 0) {
      await db.$transaction(updates);
      console.log(
        `Normalized ${updates.length} slides for project ${project.id}`,
      );
    }
  }
}

normalizeSlideOrders();
