/**
 * Returns the start and end Date objects for today (local time).
 *
 * @returns An object containing `todayStart` and `todayEnd` Date instances.
 *
 * @example
 * ```typescript
 * const { todayStart, todayEnd } = getTodayStartAndEnd();
 * console.log(todayStart); // e.g., 2024-06-07T00:00:00.000Z
 * console.log(todayEnd);   // e.g., 2024-06-07T23:59:59.999Z
 * ```
 */
export function getTodayStartAndEnd() {
  const now = new Date();
  const todayStart = new Date(
    now.getFullYear(),
    now.getMonth(),
    now.getDate(),
    0,
    0,
    0,
    0,
  );
  const todayEnd = new Date(
    now.getFullYear(),
    now.getMonth(),
    now.getDate(),
    23,
    59,
    59,
    999,
  );

  return { todayStart, todayEnd };
}
