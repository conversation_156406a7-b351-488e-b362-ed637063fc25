import imageCompression, { type Options } from "browser-image-compression";

export async function compressImage(file: File): Promise<File> {
  const options: Options = {
    maxSizeMB: 1, // Max file size in MB
    maxWidthOrHeight: 1920, // Max width/height in pixels
    useWebWorker: true,
    fileType: "image/webp",
  };

  try {
    console.log("file: ", file);
    const compressedFile = await imageCompression(file, options);
    console.log("compressedFile: ", compressedFile);
    return new File([compressedFile], file.name, {
      type: compressedFile.type,
    });
  } catch (error) {
    console.error("Image compression failed:", error);
    return file; // Return original if compression fails
  }
}
