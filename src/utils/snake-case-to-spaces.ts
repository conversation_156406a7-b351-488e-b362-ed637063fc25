/**
 * Converts a snake_cased string into a human-readable string with spaces.
 *
 * @param str - The input string in snake_case format.
 * @returns A new string where all underscores (`_`) are replaced by spaces.
 *
 * @example
 * // returns 'hello world'
 * snakeToSpaces('hello_world')
 */
export function snakeToSpaces(str?: string) {
  // Replace every underscore with a space
  return str?.replace(/_/g, " ") ?? "";
}
