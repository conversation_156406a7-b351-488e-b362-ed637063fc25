/**
 * Checks if a value is considered empty
 * @template T - The type of value being checked
 * @param value - The value to check for emptiness
 * @returns true if the value is considered empty, false otherwise
 */
export const isEmpty = <T>(value: T): boolean => {
  if (value === null || value === undefined) {
    return true;
  }

  if (typeof value === "string" || Array.isArray(value)) {
    return value.length === 0;
  }

  if (typeof value === "object") {
    if (value instanceof Map || value instanceof Set) {
      return value.size === 0;
    }
    return Object.keys(value as object).length === 0;
  }

  if (typeof value === "number") {
    return Number.isNaN(value);
  }

  return false;
};
