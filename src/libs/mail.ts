import ChangeEmailTemplate from "@/emails/templates/change-email-template";
import InvitationEmailTemplate from "@/emails/templates/invitation-email-template";
import PasswordResetEmailTemplate from "@/emails/templates/password-reset-email-template";
import VerifyEmailTemplate from "@/emails/templates/verify-email-template";
import { env } from "@/env";
import { APP_NAME } from "@/libs/constants";
import { render } from "@react-email/components";
import { Resend } from "resend";

export const resend = new Resend(env.RESEND_API_KEY);

const emailFrom = `${APP_NAME} <<EMAIL>>`;

export async function sendPasswordResetEmail(email: string, link: string) {
  const emailTemplate = PasswordResetEmailTemplate({ email, link });

  const text = await render(emailTemplate, { plainText: true });

  return await resend.emails.send({
    subject: "Reset your password",
    from: emailFrom,
    to: email,
    text,
    react: emailTemplate,
  });
}

export async function sendVerifyEmail(email: string, link: string) {
  const emailTemplate = VerifyEmailTemplate({ email, link });

  const text = await render(emailTemplate, { plainText: true });

  return await resend.emails.send({
    subject: "Verify your email address",
    from: emailFrom,
    to: email,
    text,
    react: emailTemplate,
  });
}

export async function sendEmailChangeEmail(
  email: string,
  newEmail: string,
  link: string,
) {
  const emailTemplate = ChangeEmailTemplate({ email: newEmail, link });

  const text = await render(emailTemplate, { plainText: true });

  return await resend.emails.send({
    subject: "Change your email address",
    from: emailFrom,
    to: email,
    text,
    react: emailTemplate,
  });
}

type InviteEmailParams = {
  inviteeEmail: string;
  invitationLink: string;
  organizationName: string;
  inviterName: string;
  inviterEmail: string;
};

export async function sendOrganizationInvitationEmail({
  inviteeEmail,
  invitationLink,
  organizationName,
  inviterName,
  inviterEmail,
}: InviteEmailParams) {
  const emailTemplate = InvitationEmailTemplate({
    organizationName,
    inviterName,
    inviteeEmail,
    invitationLink,
  });

  const text = await render(emailTemplate, { plainText: true });

  return await resend.emails.send({
    subject: `You've been invited to join the ${organizationName} organization on ${APP_NAME}`,
    from: emailFrom,
    to: inviteeEmail,
    text,
    replyTo: inviterEmail,
    react: emailTemplate,
  });
}
