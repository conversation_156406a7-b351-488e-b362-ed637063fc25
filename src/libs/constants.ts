export const APP_NAME = "SlydeShow";

export const FILTER_TAKE = 20;

export const PROJECT_STATUS = {
  EDITING: "editing",
  PENDING_APPROVAL: "pending_approval",
  APPROVED: "approved",
  ACTIVE: "active",
  REJECTED: "rejected",
  COMPLETED: "completed", // For projects past their end date
} as const;

export const APPROVAL_STATUS = {
  PENDING: "pending",
  APPROVED: "approved",
  REJECTED: "rejected",
} as const;

export const NOTIFICATION_TYPES = {
  APPROVAL_REQUESTED: "approval_requested",
  APPROVED: "approved",
  REJECTED: "rejected",
} as const;

export const APP_ROUTES = {
  LOGIN: "/auth/login",
  SIGN_UP: "/auth/signup",
  FORGOT_PASSWORD: "/auth/forgot-password",
  RESET_PASSWORD: "/auth/reset-password",
  DASHBOARD: "/dashboard",
  SETTINGS: "/settings",
  ORGANIZATIONS: "/organizations",
  CREATE_ORGANIZATION: "/organizations/create",
} as const;

/**
 * An array of routes that are not run through the middleware
 * These routes do not necessarily require authentication
 */
export const publicRoutes = [
  "/api/webhooks/stripe",
  "/api/invitation",
  "/api/avatar",
  "/view",
];

/**
 * An array of routes that are used for authentication
 * These routes will redirect logged in users to /dashboard
 */
export const authRoutes = [
  "/auth/login",
  "/auth/signup",
  "/auth/forgot-password",
  "/auth/set-password",
];

/**
 * The prefix for API authentication routes
 * Routes that start with this prefix are used for API authentication purposes
 */
export const apiAuthPrefix = "/api/auth";

/**
 * The prefix for TRPC routes
 * Routes that start with this prefix are used for TRPC purposes
 */
export const trpcPrefix = "/api/trpc";
