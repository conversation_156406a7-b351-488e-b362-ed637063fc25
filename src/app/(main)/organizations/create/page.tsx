import { OrganizationCreateBackButton } from "@/components/organizations/organization-create-back-button";
import { OrganizationCreateForm } from "@/components/organizations/organization-create-form";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export default async function OrganizationCreatePage() {
  return (
    <div className="flex flex-col items-center justify-center">
      <Card className="p-4">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">Create an organization</CardTitle>
          <CardDescription>
            Create a new organization to manage your projects and collaborate
            with your team.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <OrganizationCreateForm />
        </CardContent>
      </Card>
      <div className="mt-6">
        <OrganizationCreateBackButton />
      </div>
    </div>
  );
}
