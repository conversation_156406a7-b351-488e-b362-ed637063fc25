import { OrganizationAcceptInvitationButton } from "@/components/organizations/organization-accept-invitation-button";
import { Card } from "@/components/ui/card";
import { PageWrapper } from "@/components/ui/page-structure";
import { db } from "@/server/db";
import Image from "next/image";

interface Props {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

async function getOrganizationById(orgId: string) {
  const organization = await db.organization.findUnique({
    where: {
      id: orgId,
    },
    select: {
      slug: true,
      logo: true,
    },
  });
  return organization;
}

export default async function InvitePage(props: Props) {
  const searchParams = await props.searchParams;

  const orgName = searchParams?.organization as string;
  const orgId = searchParams?.organizationId as string;
  const inviteId = searchParams?.inviteId as string;

  const organization = await getOrganizationById(orgId);

  return (
    <PageWrapper className="flex min-h-[80svh] flex-row items-center justify-center">
      <Card className="max-w-md px-10 pt-10 pb-12 text-center">
        <div className="flex justify-center">
          {organization?.logo && (
            <Image
              src={organization?.logo ?? ""}
              alt={orgName}
              width={80}
              height={80}
            />
          )}
          {!organization?.logo && (
            <p className="text-2xl font-semibold">{orgName}</p>
          )}
        </div>
        <h3 className="mt-7 text-xl font-semibold">Organization Invitation</h3>
        <p className="text-dark-400 mt-3 leading-normal">
          You&apos;ve been invited to join and collaborate on the{" "}
          <span className="font-semibold">{orgName}</span> organization.
        </p>

        <div className="mt-7">
          <OrganizationAcceptInvitationButton
            inviteId={inviteId}
            slug={organization?.slug ?? ""}
          />
        </div>
      </Card>

      {/* {error && (
        <Card className="max-w-md p-10 text-center">
          <div className="flex justify-center">
            <Logo noLink />
          </div>
          <h3 className="mt-7 text-xl font-semibold">
            {error.message === "Invalid invitation" && "Invalid invitation"}
            {error.message === "Invitation expired" && "Invitation expired"}
          </h3>
          <p className="text-dark-400 mt-3 leading-normal">
            {error.message === "Invalid invitation" &&
              "The invitation does not exist or is invalid. Please contact the organization owner to get a new invitation."}
            {error.message === "Invitation expired" &&
              "The invitation is expired. Please contact the organization owner to get a new invitation."}
          </p>
        </Card>
      )} */}
    </PageWrapper>
  );
}
