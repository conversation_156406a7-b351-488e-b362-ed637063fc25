import { ProjectSlideshow } from "@/components/projects/project-slideshow";
import { getLocationSlides } from "@/server/actions/get-slides";

export interface Props {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function ViewPage(props: Props) {
  const searchParams = await props.searchParams;
  const locationId = (searchParams?.location as string) ?? "";
  const subLocationId = (searchParams?.["sub-location"] as string) ?? "";
  const slides = await getLocationSlides(locationId, subLocationId);
  return (
    <ProjectSlideshow
      initialSlides={slides}
      locationId={locationId}
      subLocationId={subLocationId}
      kioskMode={false}
    />
  );
}
