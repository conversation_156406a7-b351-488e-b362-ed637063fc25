import { ProjectView } from "@/components/projects/project-view";
import { APP_NAME } from "@/libs/constants";
import { api } from "@/trpc/server";

interface Props {
  params: Promise<{ projectId: string }>;
}

export async function generateMetadata({ params }: Props) {
  const { projectId } = await params;
  const project = await api.projects.getById({ id: projectId });
  return {
    title: `${project.name} - ${APP_NAME}`,
  };
}

export default async function ProjectPage({ params }: Props) {
  const { projectId } = await params;

  return (
    <div className="min-h-svh">
      <ProjectView projectId={projectId} />
    </div>
  );
}
