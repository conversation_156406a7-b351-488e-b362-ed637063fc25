import { SettingsMembersView } from "@/components/settings/organization/settings-members-view";
import { APP_NAME } from "@/libs/constants";

export const metadata = {
  title: `Team Members - ${APP_NAME}`,
};

interface Props {
  params: Promise<{ slug: string }>;
}

export default async function SettingsMembersPage({ params }: Props) {
  const { slug } = await params;
  return <SettingsMembersView slug={slug} />;
}
