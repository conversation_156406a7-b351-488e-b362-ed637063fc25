import { SettingsLayout } from "@/components/settings/organization/settings-layout";
import { APP_NAME } from "@/libs/constants";

export const metadata = {
  title: `Settings - ${APP_NAME}`,
};

export default async function SettingsPageLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ slug: string }>;
}) {
  const { slug } = await params;

  return (
    <SettingsLayout slug={slug}>
      <div>{children}</div>
    </SettingsLayout>
  );
}
