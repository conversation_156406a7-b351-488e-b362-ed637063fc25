import { AccountLayout } from "@/components/settings/account/account-layout";
import { APP_NAME } from "@/libs/constants";

export const metadata = {
  title: `Account Settings - ${APP_NAME}`,
};

export default async function AccountPageLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ slug: string }>;
}) {
  const { slug } = await params;

  return (
    <AccountLayout slug={slug}>
      <div>{children}</div>
    </AccountLayout>
  );
}
