"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useLocalStorage } from "@/hooks/use-local-storage";
import { useRouter } from "next/navigation";

export default function NotFound() {
  const router = useRouter();
  const [selectedOrg] = useLocalStorage("selectedOrg");

  const handleBackNavigation = () => {
    if (selectedOrg) {
      return router.push(`/${selectedOrg}/dashboard`);
    }
    return router.push("/organizations");
  };

  return (
    <main className="flex min-h-full flex-col items-center justify-center bg-white px-6 py-24 sm:py-32 lg:px-8">
      <div className="text-center">
        <p className="text-base font-semibold text-gray-600">404</p>
        <h1 className="mt-4 text-5xl font-semibold tracking-tight text-balance text-gray-900 sm:text-7xl">
          Page not found
        </h1>
        <p className="mt-6 text-lg font-medium text-pretty text-gray-500 sm:text-xl/8">
          Sorry, we couldn’t find the page you’re looking for.
        </p>
        <div className="mt-10 flex items-center justify-center gap-x-6">
          <Button onClick={handleBackNavigation} size="lg">
            Go back home
          </Button>
        </div>
      </div>
    </main>
  );
}
