import { But<PERSON> } from "@/components/ui/button";
import { IconLockOff } from "@tabler/icons-react";

export default function Forbidden() {
  return (
    <main className="flex min-h-full flex-col items-center justify-center bg-white px-6 py-24 sm:py-32 lg:px-8">
      <div className="text-center">
        <IconLockOff className="mx-auto size-24" />
        <h1 className="mt-8 text-5xl font-semibold tracking-tight text-balance text-gray-900 sm:text-7xl">
          Forbidden
        </h1>
        <p className="mt-6 text-lg font-medium text-pretty text-gray-500 sm:text-xl/8">
          Sorry you don’t have access to this organization.
        </p>
        <div className="mt-10 flex items-center justify-center gap-x-6">
          <Button href="/onboarding/organization" size="lg">
            Go back home
          </Button>
        </div>
      </div>
    </main>
  );
}
