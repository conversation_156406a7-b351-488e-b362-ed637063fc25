import { env } from "@/env";
import { getAuthSession } from "@/libs/auth";
import { db } from "@/server/db";
import { redirect } from "next/navigation";
import { NextRequest } from "next/server";

async function findUserByEmail(email: string) {
  const user = await db.user.findUnique({
    where: { email },
  });

  if (user) {
    return true;
  }

  return false;
}

export async function GET(request: NextRequest) {
  const { session } = await getAuthSession();

  const orgName = request.nextUrl.searchParams.get("organization") as string;
  const orgId = request.nextUrl.searchParams.get("organizationId") as string;
  const inviteId = request.nextUrl.searchParams.get("inviteId") as string;
  const email = request.nextUrl.searchParams.get("email") as string;
  const role = request.nextUrl.searchParams.get("role") as string;

  const searchParams = new URLSearchParams({
    organization: orgName,
    organizationId: orgId,
    inviteId,
    email,
    role,
  });

  const callbackUrl = `${env.APP_BASE_URL}/invitation?${searchParams.toString()}`;
  //   const callbackUrl = `${env.APP_BASE_URL}/api/invitation?${searchParams.toString()}`;

  const encodedCallbackUrl = encodeURIComponent(callbackUrl);

  if (!session) {
    const userExists = await findUserByEmail(email);

    if (!userExists) {
      redirect(
        `${env.APP_BASE_URL}/auth/signup?callbackUrl=${encodedCallbackUrl}&email=${encodeURIComponent(email)}&organization=${encodeURIComponent(orgName)}`,
      );
    } else {
      redirect(
        `${env.APP_BASE_URL}/auth/login?callbackUrl=${encodedCallbackUrl}`,
      );
    }
  }

  if (session) {
    redirect(`${callbackUrl}`);
  }

  return new Response("ok", { status: 200 });
}
