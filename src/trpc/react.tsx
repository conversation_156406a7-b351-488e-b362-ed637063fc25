"use client";

import { QueryClientProvider, type QueryClient } from "@tanstack/react-query";
import { httpBatchStreamLink, loggerLink } from "@trpc/client";
import { createTRPCReact } from "@trpc/react-query";
import { type inferRouterInputs, type inferRouterOutputs } from "@trpc/server";
import { useState } from "react";
import SuperJSON from "superjson";

import { type AppRouter } from "@/server/api/root";
import { createQueryClient } from "./query-client";

let clientQueryClientSingleton: QueryClient | undefined = undefined;
const getQueryClient = () => {
  if (typeof window === "undefined") {
    // Server: always make a new query client
    return createQueryClient();
  }
  // Browser: use singleton pattern to keep the same query client
  return (clientQueryClientSingleton ??= createQueryClient());
};

export const api = createTRPCReact<AppRouter>();

/**
 * Inference helper for inputs.
 *
 * @example type HelloInput = RouterInputs['example']['hello']
 */
export type RouterInputs = inferRouterInputs<AppRouter>;

/**
 * Inference helper for outputs.
 *
 * @example type HelloOutput = RouterOutputs['example']['hello']
 */
export type RouterOutputs = inferRouterOutputs<AppRouter>;

/**
 * Initializes the TRPC client.
 *
 * This hook creates a TRPC client using React's useState hook, ensuring that the client instance remains
 * stable across component re-renders. The client is configured with two links:
 *
 * - loggerLink: Logs operations when in development mode or if a downstream operation results in an error.
 * - httpBatchStreamLink: Handles HTTP batching and streaming using SuperJSON for transformation.
 *
 * The HTTP link is further customized with a URL constructed by combining the base URL (obtained via getBaseUrl())
 * with the API endpoint, and sets a custom header "x-trpc-source" to indicate that the source of the request is "nextjs-react".
 *
 * @remarks
 * - Ensure that process.env.NODE_ENV, getBaseUrl(), and any necessary environment variables are properly configured.
 * - This setup is particularly useful for environments where logging and efficient client-server communication via batch streaming
 *   are required.
 *
 * @example
 * const [trpcClient] = useState(() =>
 *   api.createClient({
 *     links: [
 *       loggerLink({ ... }),
 *       httpBatchStreamLink({ ... }),
 *     ],
 *   }),
 * );
 *
 * @returns A configured TRPC client instance with logging and HTTP batch streaming capabilities.
 */
export function TRPCReactProvider(props: { children: React.ReactNode }) {
  const queryClient = getQueryClient();

  const [trpcClient] = useState(() =>
    api.createClient({
      links: [
        loggerLink({
          enabled: (op) =>
            process.env.NODE_ENV === "development" ||
            (op.direction === "down" && op.result instanceof Error),
        }),
        httpBatchStreamLink({
          transformer: SuperJSON,
          url: getBaseUrl() + "/api/trpc",
          headers: () => {
            const headers = new Headers();
            headers.set("x-trpc-source", "nextjs-react");
            return headers;
          },
        }),
      ],
    }),
  );

  return (
    <api.Provider client={trpcClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>
        {props.children}
      </QueryClientProvider>
    </api.Provider>
  );
}

function getBaseUrl() {
  if (typeof window !== "undefined") return window.location.origin;
  if (process.env.VERCEL_URL) return `https://${process.env.VERCEL_URL}`;
  return `http://localhost:${process.env.PORT ?? 3000}`;
}
