"use client";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type { User } from "@/types/admin-user.types";
import { formatDate } from "@/utils/format-date";
import { getInitials } from "@/utils/get-initials";
import {
  IconCopy,
  IconDots,
  IconEdit,
  IconEye,
  IconTrash,
  IconUser,
  IconUserCheck,
  IconUserOff,
  IconUserShield,
} from "@tabler/icons-react";
import type { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown } from "lucide-react";

export interface UsersTableActions {
  onEdit: (user: User) => void;
  onBan: (user: User) => void;
  onUnban: (user: User) => void;
  onDelete: (user: User) => void;
  onSetRole: (user: User, role: "admin" | "user") => void;
  onImpersonate: (user: User) => void;
  onViewSessions: (user: User) => void;
}

// Helper function to check if user has admin role
const hasAdminRole = (role: User["role"]): boolean => {
  if (!role) return false;
  if (typeof role === "string") return role === "admin";
  if (Array.isArray(role)) {
    return (role as string[]).some((r) => r === "admin");
  }
  return false;
};

// Helper function to get display role
const getDisplayRole = (role: User["role"]): string => {
  if (!role) return "user";
  if (typeof role === "string") return role;
  if (Array.isArray(role)) return (role as string[])[0] || "user";
  return "user";
};

export const createUsersColumns = (
  actions: UsersTableActions,
): ColumnDef<User>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="-ml-4 h-8"
          rightIcon={<ArrowUpDown className="size-4" />}
        >
          Name
        </Button>
      );
    },
    cell: ({ row }) => {
      const user = row.original;
      const initials = getInitials(user.name || user.email);

      return (
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            {user.image && (
              <AvatarImage src={user.image} alt={user.name || user.email} />
            )}
            <AvatarFallback className="bg-gradient-to-r from-blue-500 to-purple-600 text-xs font-medium text-white">
              {initials}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium">{user.name || "Unknown"}</div>
            <div className="text-sm text-muted-foreground">{user.email}</div>
          </div>
        </div>
      );
    },
    // import { generateAvatarImage } from "@/utils/generate-user-image";
  },
  {
    accessorKey: "email",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="-ml-4 h-8"
          rightIcon={<ArrowUpDown className="size-4" />}
        >
          Email
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div className="font-medium">{row.getValue("email")}</div>;
    },
  },
  {
    accessorKey: "role",
    header: "Role",
    cell: ({ row }) => {
      const role = row.getValue("role") as User["role"];
      const displayRole = getDisplayRole(role);

      return (
        <Badge
          variant={displayRole === "admin" ? "blue" : "gray"}
          className="capitalize"
        >
          {displayRole}
        </Badge>
      );
    },
  },
  {
    accessorKey: "banned",
    header: "Status",
    cell: ({ row }) => {
      const user = row.original;
      const isBanned = user.banned;

      return isBanned ? (
        <Badge variant="red">Banned</Badge>
      ) : (
        <Badge variant="green">Active</Badge>
      );
    },
  },
  {
    id: "banReason",
    accessorKey: "banReason",
    header: "Ban Reason",
    cell: ({ row }) => {
      const banReason = row.getValue("banReason") as string;
      return banReason ? (
        <div className="max-w-[200px] truncate" title={banReason}>
          {banReason}
        </div>
      ) : (
        <span className="text-muted-foreground">-</span>
      );
    },
  },
  {
    id: "banExpires",
    accessorKey: "banExpires",
    header: "Ban Expires",
    cell: ({ row }) => {
      const banExpires = row.getValue("banExpires") as string;
      return banExpires ? (
        <div className="text-sm">{formatDate(banExpires)}</div>
      ) : (
        <span className="text-muted-foreground">-</span>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const user = row.original;
      const isBanned = user.banned;
      const isAdmin = hasAdminRole(user.role);

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="size-7 text-gray-400 data-[state=open]:bg-accent data-[state=open]:text-gray-900"
              title="Open project actions menu"
            >
              <IconDots size={20} />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[180px]">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(user.id)}
            >
              <IconCopy className="mr-1 size-4" /> Copy user ID
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(user.email)}
            >
              <IconCopy className="mr-1 size-4" /> Copy email
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => actions.onEdit(user)}>
              <IconEdit className="mr-1 size-4" /> Edit user
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() =>
                actions.onSetRole(user, isAdmin ? "user" : "admin")
              }
            >
              {isAdmin ? (
                <>
                  <IconUserOff className="mr-1 size-4" /> Remove admin
                </>
              ) : (
                <>
                  <IconUserShield className="mr-1 size-4" /> Make admin
                </>
              )}
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => actions.onViewSessions(user)}>
              <IconEye className="mr-1 size-4" /> View sessions
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => actions.onImpersonate(user)}>
              <IconUserCheck className="mr-1 size-4" /> Impersonate user
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            {isBanned ? (
              <DropdownMenuItem onClick={() => actions.onUnban(user)}>
                <IconUser className="mr-1 size-4" /> Unban user
              </DropdownMenuItem>
            ) : (
              <DropdownMenuItem onClick={() => actions.onBan(user)}>
                <IconUserOff className="mr-1 size-4" /> Ban user
              </DropdownMenuItem>
            )}
            <DropdownMenuItem
              onClick={() => actions.onDelete(user)}
              className="!text-destructive hover:!bg-destructive/5"
            >
              <IconTrash className="mr-1 size-4" /> Delete user
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
