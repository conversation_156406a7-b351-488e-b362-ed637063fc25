"use client";

import { BanUserDialog } from "@/components/admin/user/ban-user-dialog";
import { ConfirmationDialog } from "@/components/admin/user/confirmation-dialog";
import { CreateUserDialog } from "@/components/admin/user/create-user-dialog";
import { EditUserDialog } from "@/components/admin/user/edit-user-dialog";
import { DataTable } from "@/components/admin/user/users-data-table";
import {
  createUsersColumns,
  type UsersTableActions,
} from "@/components/admin/user/users-table-columns";
import { Button } from "@/components/ui/button";
import {
  PageContent,
  PageTitle,
  PageWrapper,
} from "@/components/ui/page-structure";
import { useDebouncedState } from "@/hooks/use-debounced-state";
import { useDialog } from "@/hooks/use-dialog";
import {
  useImpersonateUser,
  useListUsers,
  useSetUserRole,
} from "@/queries/admin-user.queries";
import type { User } from "@/types/admin-user.types";
import { Plus, RefreshCw } from "lucide-react";
import * as React from "react";
import { toast } from "sonner";

export function UsersView() {
  const [searchValue, setSearchValue] = React.useState("");
  const [debouncedSearchValue] = useDebouncedState(searchValue, 300);
  const [currentPage, setCurrentPage] = React.useState(0);
  const [pageSize] = React.useState(10);

  // Dialog states using useDialog
  const [createDialogOpen, createDialogActions] = useDialog();
  const [editDialogOpen, editDialogActions] = useDialog();
  const [banDialogOpen, banDialogActions] = useDialog();
  const [deleteDialogOpen, deleteDialogActions] = useDialog();
  const [unbanDialogOpen, unbanDialogActions] = useDialog();
  const [selectedUser, setSelectedUser] = React.useState<User | null>(null);

  // Mutations
  const setRoleMutation = useSetUserRole();
  const impersonateMutation = useImpersonateUser();

  // Query for users list
  const {
    data: usersData,
    isLoading,
    error,
    refetch,
  } = useListUsers({
    searchValue: searchValue || undefined,
    searchField: "email",
    limit: pageSize,
    offset: currentPage * pageSize,
  });

  const users = usersData?.users || [];
  const totalUsers = usersData?.total || 0;

  // Table actions
  const tableActions: UsersTableActions = React.useMemo(
    () => ({
      onEdit: (user: User) => {
        setSelectedUser(user);
        editDialogActions.open();
      },
      onBan: (user: User) => {
        setSelectedUser(user);
        banDialogActions.open();
      },
      onUnban: (user: User) => {
        setSelectedUser(user);
        unbanDialogActions.open();
      },
      onDelete: (user: User) => {
        setSelectedUser(user);
        deleteDialogActions.open();
      },
      onSetRole: async (user: User, role: "admin" | "user") => {
        try {
          await setRoleMutation.mutateAsync({
            userId: user.id,
            role,
          });
          toast.success(`User role updated to ${role}`);
        } catch (error) {
          console.error("Error updating user role:", error);
          toast.error("Failed to update user role");
        }
      },
      onImpersonate: async (user: User) => {
        try {
          await impersonateMutation.mutateAsync({
            userId: user.id,
          });
          toast.success(`Now impersonating ${user.name || user.email}`);
          window.open("/organizations", "_blank");
        } catch (error) {
          console.error("Error impersonating user:", error);
          toast.error("Failed to impersonate user");
        }
      },
      onViewSessions: (_user: User) => {
        toast.info("View sessions functionality coming soon");
      },
    }),
    [
      setRoleMutation,
      impersonateMutation,
      editDialogActions,
      banDialogActions,
      unbanDialogActions,
      deleteDialogActions,
    ],
  );

  const columns = React.useMemo(
    () => createUsersColumns(tableActions),
    [tableActions],
  );

  // Reset to first page when search changes (debounced)
  React.useEffect(() => {
    setCurrentPage(0);
  }, [debouncedSearchValue]);

  const handleRefresh = () => {
    refetch();
    toast.success("Users list refreshed");
  };

  return (
    <PageWrapper>
      <div className="flex items-center justify-between">
        <PageTitle>Users</PageTitle>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            loading={isLoading}
            leftIcon={<RefreshCw className="h-4 w-4" />}
            disabled={isLoading}
          >
            Refresh
          </Button>
          <Button
            type="button"
            leftIcon={<Plus className="h-4 w-4" />}
            onClick={createDialogActions.open}
          >
            Add User
          </Button>
        </div>
      </div>

      <PageContent>
        {error ? (
          <div className="rounded-md border border-destructive/50 p-6 text-center">
            <h3 className="mb-2 text-lg font-semibold text-destructive">
              Error Loading Users
            </h3>
            <p className="mb-4 text-sm text-muted-foreground">
              There was an error loading the users list. Please try again.
            </p>
            <Button
              variant="outline"
              type="button"
              leftIcon={<RefreshCw className="h-4 w-4" />}
              onClick={handleRefresh}
            >
              Try Again
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <p className="text-sm text-muted-foreground">
                {isLoading ? "Loading..." : `${totalUsers} users total`}
              </p>
            </div>

            <DataTable
              columns={columns}
              data={users}
              searchValue={searchValue}
              onSearchChange={setSearchValue}
              searchPlaceholder="Search users by email..."
              isLoading={isLoading}
            />
          </div>
        )}
      </PageContent>

      {/* Dialogs */}
      <CreateUserDialog
        open={createDialogOpen}
        onOpenChange={createDialogActions.toggle}
      />

      <EditUserDialog
        user={selectedUser}
        open={editDialogOpen}
        onOpenChange={editDialogActions.toggle}
      />

      <BanUserDialog
        user={selectedUser}
        open={banDialogOpen}
        onOpenChange={banDialogActions.toggle}
      />

      <ConfirmationDialog
        user={selectedUser}
        open={deleteDialogOpen}
        onOpenChange={deleteDialogActions.toggle}
        type="delete"
      />

      <ConfirmationDialog
        user={selectedUser}
        open={unbanDialogOpen}
        onOpenChange={unbanDialogActions.toggle}
        type="unban"
      />
    </PageWrapper>
  );
}
