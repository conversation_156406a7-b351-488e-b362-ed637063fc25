"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useRemoveUser, useUnbanUser } from "@/queries/admin-user.queries";
import type { User } from "@/types/admin-user.types";
import * as React from "react";
import { toast } from "sonner";

interface ConfirmationDialogProps {
  user: User | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  type: "delete" | "unban";
}

export function ConfirmationDialog({
  user,
  open,
  onOpenChange,
  type,
}: ConfirmationDialogProps) {
  const removeUserMutation = useRemoveUser();
  const unbanUserMutation = useUnbanUser();

  const [isLoading, setIsLoading] = React.useState(false);

  const handleConfirm = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      if (type === "delete") {
        await removeUserMutation.mutateAsync({ userId: user.id });
        toast.success(`User ${user.name || user.email} has been deleted`);
      } else if (type === "unban") {
        await unbanUserMutation.mutateAsync({ userId: user.id });
        toast.success(`User ${user.name || user.email} has been unbanned`);
      }
      onOpenChange(false);
    } catch (error) {
      console.error(`Error ${type}ing user:`, error);
      toast.error(`Failed to ${type} user`);
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) return null;

  const isDelete = type === "delete";

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{isDelete ? "Delete User" : "Unban User"}</DialogTitle>
          <DialogDescription>
            {isDelete ? (
              <>
                Are you sure you want to delete {user.name || user.email}? This
                action cannot be undone and will permanently remove the user and
                all associated data.
              </>
            ) : (
              <>
                Are you sure you want to unban {user.name || user.email}? This
                will restore their access to the system.
              </>
            )}
          </DialogDescription>
        </DialogHeader>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="button"
            variant={isDelete ? "destructive" : "default"}
            onClick={handleConfirm}
            disabled={isLoading}
          >
            {isLoading
              ? isDelete
                ? "Deleting..."
                : "Unbanning..."
              : isDelete
                ? "Delete User"
                : "Unban User"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
