"use client";

import { ChevronsUpDown, Plus } from "lucide-react";

import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { Skeleton } from "@/components/ui/skeleton";
import { useSetLocalStorage } from "@/hooks/use-local-storage";
import { useOrganizationSlug } from "@/hooks/use-organization-slug";
import { APP_ROUTES } from "@/libs/constants";
import {
  useOrganizationBySlug,
  useOrganizations,
} from "@/queries/organization.queries";
import { getInitials } from "@/utils/get-initials";
import { AvatarImage } from "@radix-ui/react-avatar";
import { IconCheck } from "@tabler/icons-react";
import Link from "next/link";

export function OrganizationSwitcher() {
  const setSelectedOrg = useSetLocalStorage();
  const slug = useOrganizationSlug();
  const { isMobile } = useSidebar();

  const orgsQuery = useOrganizations();
  const activeOrgQuery = useOrganizationBySlug(slug);

  if (!orgsQuery.data || !activeOrgQuery.data) {
    return <Skeleton className="h-12 w-full bg-sidebar-accent" />;
  }

  const organizations = orgsQuery.data.data;
  const activeOrganization = activeOrgQuery.data;

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              {activeOrganization && (
                <Avatar className="rounded-lg">
                  <AvatarImage
                    src={activeOrganization.logo ?? ""}
                    alt={activeOrganization.name}
                  />
                  <AvatarFallback className="rounded-lg">
                    {getInitials(activeOrganization.name, 1)}
                  </AvatarFallback>
                </Avatar>
              )}

              <div className="ml-1 grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">
                  {activeOrganization?.name}
                </span>
                {/* <span className="truncate text-xs">
                  {activeOrgnaization?.plan}
                </span> */}
              </div>
              <ChevronsUpDown className="ml-auto" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
            align="start"
            side={isMobile ? "bottom" : "right"}
            sideOffset={4}
          >
            <DropdownMenuLabel className="text-xs text-muted-foreground">
              Organizations
            </DropdownMenuLabel>
            {organizations?.map((organization) => (
              <Link
                href={`/${organization.slug}/dashboard`}
                key={organization.id}
                onNavigate={() => {
                  setSelectedOrg("selectedOrg", organization.slug);
                }}
              >
                <DropdownMenuItem key={organization.id} className="gap-4 p-2">
                  <div className="flex items-center gap-4">
                    <div className="flex size-6 items-center justify-center rounded-sm border">
                      <Avatar className="rounded-lg">
                        <AvatarImage
                          src={organization.logo ?? ""}
                          alt={organization.name}
                        />
                        <AvatarFallback className="rounded-lg">
                          {getInitials(organization.name, 1)}
                        </AvatarFallback>
                      </Avatar>
                    </div>
                    {organization.name}
                  </div>
                  {activeOrganization?.slug === organization.slug && (
                    <IconCheck className="ml-auto text-muted-foreground" />
                  )}
                </DropdownMenuItem>
              </Link>
            ))}
            <DropdownMenuSeparator />
            <Link href={APP_ROUTES.CREATE_ORGANIZATION}>
              <DropdownMenuItem className="gap-2 p-2">
                <Plus className="size-4" />
                <div className="font-medium">Create organization</div>
              </DropdownMenuItem>
            </Link>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
