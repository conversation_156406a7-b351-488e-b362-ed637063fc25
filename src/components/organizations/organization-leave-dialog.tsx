import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  Di<PERSON><PERSON>ontent,
  Di<PERSON><PERSON>ooter,
  DialogHeader,
  type DialogProps,
  DialogTitle,
} from "@/components/ui/dialog";

interface Props extends DialogProps {
  onDelete: () => Promise<void>;
  onClose: () => void;
  loading?: boolean;
}

export function OrganizationLeaveDialog({
  onClose,
  open,
  onDelete,
  loading,
}: Props) {
  function closeModal() {
    onClose();
  }

  async function handleLeave() {
    try {
      await onDelete();
      closeModal();
    } catch (error) {
      console.log(error);
    }
  }

  return (
    <Dialog open={open} onOpenChange={closeModal}>
      <DialogContent className="sm:max-w-[450px]">
        <DialogHeader>
          <DialogTitle>Leave organization</DialogTitle>
        </DialogHeader>

        <div className="prose">
          <p>
            Are you sure you want to leave this organization? You will no longer
            be able to access this organization and its projects.
          </p>
        </div>

        <DialogFooter className="mt-3">
          <Button
            variant="outline"
            onClick={(e) => {
              e.stopPropagation();
              closeModal();
            }}
            className="w-full"
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            className="w-full"
            onClick={(e) => {
              e.stopPropagation();
              handleLeave();
            }}
            loading={loading}
          >
            Leave organization
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
