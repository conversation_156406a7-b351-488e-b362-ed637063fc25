"use client";

import { Button } from "@/components/ui/button";
import { authClient } from "@/libs/auth-client";
import { api } from "@/trpc/react";
import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface Props {
  slug: string;
  inviteId: string;
  className?: string;
}

export function OrganizationAcceptInvitationButton({
  inviteId,
  slug,
  className,
}: Props) {
  const router = useRouter();
  const apiUtils = api.useUtils();

  const acceptInviteMutation = useMutation({
    mutationFn: async () => {
      return await authClient.organization.acceptInvitation({
        invitationId: inviteId,
      });
    },
    onSuccess: async (data) => {
      if (data.error) {
        return toast.error("Error", { description: data.error.message });
      }
      router.push(`/${slug}/dashboard`);
    },
    onError: (error) => {
      console.log(error);
      toast.error("Error", { description: error.message });
    },
    onSettled: async () => {
      await apiUtils.organizations.getBySlug.invalidate({ slug });
      await apiUtils.organizations.getAll.invalidate();
    },
  });

  const onSubmit = async () => {
    await acceptInviteMutation.mutateAsync();
  };

  return (
    <div>
      <Button
        className={className}
        onClick={onSubmit}
        loading={acceptInviteMutation.isPending}
        size="lg"
      >
        Accept invitation
      </Button>
    </div>
  );
}
