"use client";

import { <PERSON>ertError } from "@/components/ui/alert-error";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { env } from "@/env";
import { nanoid } from "@/libs/nanoid";
import { type z } from "@/libs/zod";
import {
  checkSlug,
  useOrganizationAddMutation,
  useOrganizationUpdateMutation,
} from "@/queries/organization.queries";
import { OrgCreateSchema } from "@/schemas/organization.schemas";
import { slugify } from "@/utils/slugify";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";

/**
 * Asynchronously generates an avatar image for a user by sending a POST request to the avatar API.
 *
 * @param seed - A string used to seed the avatar generation process.
 * @param uniqueIdentifier - The unique identifier of the user for whom the avatar is being generated.
 * @returns A promise that resolves to the URL of the generated avatar image.
 * @throws Will throw an error if the upload fails or the response is not OK.
 */
export const createAvatarImage = async (
  seed: string,
  uniqueIdentifier: string,
) => {
  const res = await fetch(
    `${env.NEXT_PUBLIC_APP_BASE_URL}/api/avatar?resourceType=organization`,
    {
      method: "POST",
      body: JSON.stringify({
        seed,
        key: `organizations/${uniqueIdentifier}/${nanoid(10)}.svg`,
      }),
    },
  );
  // Check if the upload was successful
  if (!res.ok) {
    throw new Error(`Upload failed: ${res.status} ${res.statusText}`);
  }
  // // Get the image URL from the response
  const response = await res.json();
  // Return the image URL
  return response.url as string;
};

type FormData = z.infer<typeof OrgCreateSchema>;

export function OrganizationCreateForm() {
  const [errorMessage, setErrorMessage] = useState("");

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: zodResolver(OrgCreateSchema),
    defaultValues: {
      name: "",
      slug: "",
    },
  });

  const slug = watch("slug");

  const organizationCreateMutation = useOrganizationAddMutation();
  const updateOrganizationMutation = useOrganizationUpdateMutation();

  async function onSubmit(data: FormData) {
    setErrorMessage("");
    await organizationCreateMutation.mutateAsync(
      {
        name: data.name,
        slug: data.slug,
      },
      {
        onSuccess: async (data) => {
          const organizationLogo = await createAvatarImage(
            data?.name ?? "",
            data?.id ?? "",
          );

          await updateOrganizationMutation.mutateAsync({
            id: data?.id ?? "",
            logo: organizationLogo,
          });
        },
        onError: (error) => {
          setErrorMessage(error.message);
        },
      },
    );
  }

  async function checkOrganizationSlug(slug: string) {
    const res = await checkSlug(slug);
    if (res.error) {
      setErrorMessage("The organization slug is already in use.");
    } else {
      setErrorMessage("");
    }
  }

  return (
    <div>
      <AlertError message={errorMessage} className="mb-6" />

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid gap-6">
          <div className="grid gap-6">
            <div className="grid gap-6">
              <Input
                label="Organization name"
                {...register("name", {
                  onChange: (e) => setValue("slug", slugify(e.target.value)),
                })}
                error={errors.name !== undefined}
                errorMessage={errors?.name?.message}
                allowAutoComplete={false}
              />
              <Input
                label="Organization slug"
                {...register("slug", {
                  required: true,
                  minLength: 3,
                  maxLength: 48,
                  pattern: /^[a-zA-Z0-9\-]+$/,
                })}
                onBlur={async () => await checkOrganizationSlug(slug)}
                description="The slug is used to identify your organization in the URL. It can only contain letters, numbers, and hyphens."
                error={errors.slug !== undefined}
                errorMessage={errors?.slug?.message}
                allowAutoComplete={false}
              />
            </div>
            <Button type="submit" className="w-full" loading={isSubmitting}>
              Create organization
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
}
