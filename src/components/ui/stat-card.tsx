import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { cn } from "@/libs/utils";
import Link from "next/link";

interface Props {
  title: string;
  subTitle: string;
  stat?: React.ReactNode;
  icon?: React.ReactNode;
  subIcon?: React.ReactNode;
  classNames?: {
    borderColor?: string;
    textColor?: string;
    iconBackgroundColor?: string;
  };
  href?: string;
}

export function StatCard({
  title,
  subTitle,
  stat,
  icon,
  subIcon,
  classNames,
  href,
}: Props) {
  return (
    <Card
      className={cn(
        "group relative overflow-hidden",
        classNames?.borderColor && `border-l-4`,
        classNames?.borderColor,
      )}
    >
      <CardHeader className="pt-5 pb-2">
        <div className="flex items-center justify-between">
          {href ? (
            <Link href={href}>
              <CardTitle className="text-sm font-medium text-muted-foreground transition-colors group-hover:text-gray-700">
                {title}
              </CardTitle>
            </Link>
          ) : (
            <CardTitle className="text-sm font-medium text-muted-foreground transition-colors group-hover:text-gray-700">
              {title}
            </CardTitle>
          )}
          <div
            className={cn("rounded-full p-2", classNames?.iconBackgroundColor)}
          >
            {icon}
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className={cn("text-3xl font-bold", classNames?.textColor)}>
          {stat ?? 0}
        </div>
        <div className="mt-2 flex items-center gap-2 text-sm text-muted-foreground">
          {subIcon}
          <span>{subTitle}</span>
        </div>
      </CardContent>
    </Card>
  );
}
