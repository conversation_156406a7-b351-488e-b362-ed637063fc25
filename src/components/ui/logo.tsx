import LogoIconDarkSvg from "@/assets/logos/logo-dark.svg";
import LogoFullDarkSvg from "@/assets/logos/logo-full-dark.svg";
import LogoFullLightSvg from "@/assets/logos/logo-full-light.svg";
import LogoIconLightSvg from "@/assets/logos/logo-light.svg";
import { cn } from "@/libs/utils";
import Image from "next/image";
import Link from "next/link";

interface Props {
  noLink?: boolean;
  href?: string;
  className?: string;
  icon?: boolean;
}

interface LogoProps extends Props {
  variant?: "dark" | "light";
}

export function Logo({
  noLink = false,
  href = "/",
  className,
  icon = false,
  variant = "dark",
}: LogoProps) {
  if (variant === "light") {
    return (
      <LogoLight
        noLink={noLink}
        href={href}
        className={className}
        icon={icon}
      />
    );
  }
  return (
    <LogoDark noLink={noLink} href={href} className={className} icon={icon} />
  );
}

function LogoDark({
  noLink = false,
  icon = false,
  href = "/",
  className,
}: Props) {
  const src = icon ? LogoIconDarkSvg : LogoFullDarkSvg;
  if (noLink) {
    return (
      <div>
        <Image
          className={cn("", icon ? "w-6" : "w-32", className)}
          src={src}
          alt=""
          width={icon ? 32 : 80}
          height={icon ? 32 : 70}
        />
      </div>
    );
  }
  return (
    <div>
      <Link href={href}>
        <Image
          className={cn("", icon ? "w-6" : "w-32", className)}
          src={src}
          alt=""
          width={icon ? 32 : 80}
          height={icon ? 32 : 70}
        />
      </Link>
    </div>
  );
}

function LogoLight({
  noLink = false,
  icon = false,
  href = "/",
  className,
}: Props) {
  const src = icon ? LogoIconLightSvg : LogoFullLightSvg;
  if (noLink) {
    return (
      <div>
        <Image
          className={cn("", icon ? "w-6" : "w-32", className)}
          src={src}
          alt=""
          width={icon ? 32 : 80}
          height={icon ? 32 : 70}
        />
      </div>
    );
  }
  return (
    <div>
      <Link href={href}>
        <Image
          className={cn("", icon ? "w-6" : "w-32", className)}
          src={src}
          alt=""
          width={icon ? 32 : 80}
          height={icon ? 32 : 70}
        />
      </Link>
    </div>
  );
}
