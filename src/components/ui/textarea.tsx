import { cn } from "@/libs/utils";
import * as React from "react";

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  id?: string;
  label?: string;
  description?: string;
  error?: boolean;
  errorMessage?: string;
  classNames?: {
    label?: string;
  };
  styles?: {
    label?: React.CSSProperties;
  };
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      className,
      classNames,
      styles,
      label,
      description,
      id,
      error,
      errorMessage,
      ...props
    },
    ref,
  ) => {
    return (
      <div>
        {label && (
          <label
            htmlFor={id}
            className={cn(
              "block text-sm leading-6 font-medium",
              classNames?.label,
            )}
            style={styles?.label}
          >
            {label}
          </label>
        )}
        {description && (
          <p className="block text-sm text-gray-500">{description}</p>
        )}
        <div className={cn(label && "mt-[5px]")}>
          <textarea
            id={id}
            className={cn(
              "flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-xs placeholder:text-muted-foreground focus-visible:ring-1 focus-visible:ring-ring focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
              error && "border-red-500 focus-visible:ring-red-500!",
              className,
            )}
            ref={ref}
            {...props}
          />
          {error && <p className="mt-1 text-sm text-red-500">{errorMessage}</p>}
        </div>
      </div>
    );
  },
);
Textarea.displayName = "Textarea";

export { Textarea };
