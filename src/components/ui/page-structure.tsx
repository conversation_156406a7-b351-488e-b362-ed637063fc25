import { Loader } from "@/components/ui/loader";
import { cn } from "@/libs/utils";

interface Props {
  children: React.ReactNode;
  className?: string;
}

export function PageWrapper({ children, className }: Props) {
  return <div className={cn("p-4 sm:p-6 lg:p-8", className)}>{children}</div>;
}

export function PageTitle({
  className,
  children,
}: {
  className?: string;
  children: React.ReactNode;
}) {
  return (
    <h1 className={cn("text-2xl font-semibold sm:text-2xl", className)}>
      {children}
    </h1>
  );
}

export function PageDescription({
  className,
  children,
}: {
  className?: string;
  children: React.ReactNode;
}) {
  return (
    <p className={cn("mt-2 text-base text-gray-500", className)}>{children}</p>
  );
}

export function PageContent({ className, children }: Props) {
  return <div className={cn("mt-6", className)}>{children}</div>;
}

export function PageLoader({ className }: { className?: string }) {
  return (
    <div
      className={cn("flex min-h-screen items-center justify-center", className)}
    >
      <Loader />
    </div>
  );
}
