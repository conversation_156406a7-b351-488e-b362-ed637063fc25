import { Loader } from "@/components/ui/loader";
import { cn } from "@/libs/utils";
import { IMAGE_MIME_TYPE } from "@/types/utility.types";
import { bytesToMegabytes } from "@/utils/bytes-to-megabytes";
import { IconPhoto } from "@tabler/icons-react";
import { useState } from "react";
import { useDropzone, type FileRejection } from "react-dropzone";

interface Props {
  onUpload?: (file: File) => Promise<void>;
  loading?: boolean;
  maxFileSize?: number;
  className?: string;
}

export function Dropzone({
  onUpload,
  maxFileSize = 10000000,
  loading,
  className,
}: Props) {
  const [uploadError, setUploadError] = useState("");
  const [isUploadLoading, setUploadLoading] = useState(false);

  const { getRootProps, getInputProps } = useDropzone({
    disabled: isUploadLoading,
    maxSize: maxFileSize,
    maxFiles: 1,
    accept: IMAGE_MIME_TYPE.reduce((r, key) => ({ ...r, [key]: [] }), {}),
    onDropAccepted: handleFileUpload,
    onDropRejected: handleFileRejectedErrors,
  });

  function handleFileRejectedErrors(fileRejections: FileRejection[]) {
    const error = fileRejections[0]?.errors[0];
    switch (error?.code) {
      case "file-too-large": {
        setUploadError(
          `File size should not exceed ${bytesToMegabytes(maxFileSize)}mb`,
        );
        break;
      }
      case "file-invalid-type": {
        setUploadError(error.message);
        break;
      }
    }
  }

  async function handleFileUpload(files: File[]) {
    try {
      setUploadLoading(true);
      const file = files[0] as File;
      //   await onUpload(file);
      console.log(file);
      setUploadLoading(false);
    } catch (error) {
      console.log(error);
      setUploadLoading(false);
    }
  }

  return (
    <div
      {...getRootProps({
        className: cn(
          "w-full h-[300px] flex flex-col justify-center items-center rounded-lg border border-dashed border-gray-900/25 py-10 hover:bg-gray-50 cursor-pointer",
          isUploadLoading && "bg-gray-50 cursor-default",
          className,
        ),
      })}
    >
      {isUploadLoading && <Loader />}
      {!isUploadLoading && (
        <>
          <input {...getInputProps()} />
          <IconPhoto className="h-12 w-12 text-gray-700" />
          <p className="mt-4 text-lg">
            Click to choose a file or drag image here
          </p>
          <p className="mt-4 text-sm text-gray-600">
            Size limit: {bytesToMegabytes(maxFileSize)}MB
          </p>
        </>
      )}
    </div>
  );
}
