import { cn } from "@/libs/utils";

interface AlertProps {
  message?: string | null;
  className?: string;
  children?: React.ReactNode;
}

export function AlertSuccess({ message, className, children }: AlertProps) {
  if (!message && !children) return null;

  return (
    <div
      className={cn(
        "flex items-center space-x-3 rounded-lg bg-emerald-500/15 p-3 text-sm text-emerald-500",
        className,
      )}
    >
      {children ? children : <p>{message}</p>}
    </div>
  );
}
