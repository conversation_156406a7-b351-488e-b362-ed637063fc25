"use client";

import { But<PERSON> } from "@/components/ui/button";
import { IconX } from "@tabler/icons-react";
import { useRouter } from "next/navigation";

export function FullScreenBackButton() {
  const router = useRouter();

  const handleBackClick = () => {
    router.back();
  };

  return (
    <div className="absolute top-2 right-2">
      <Button variant="outline" size="icon" onClick={handleBackClick}>
        <IconX className="size-4" />
      </Button>
    </div>
  );
}
