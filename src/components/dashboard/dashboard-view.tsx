"use client";

import { ProjectStatusBadge } from "@/components/projects/project-status-badge";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  PageContent,
  PageDescription,
  PageLoader,
  PageTitle,
  PageWrapper,
} from "@/components/ui/page-structure";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { StatCard } from "@/components/ui/stat-card";
import { useOrganizationSlug } from "@/hooks/use-organization-slug";
import {
  useActivityStats,
  useProjectActivityTimeline,
} from "@/queries/activity.queries";
import {
  useOrganizationBySlug,
  useOrganizationMemberRole,
} from "@/queries/organization.queries";
import { api } from "@/trpc/react";
import type { Role } from "@/types/organization.types";
import type { ProjectStatus } from "@/types/project.types";
import { formatDateRelative } from "@/utils/format-date";
import {
  IconActivity,
  IconBuildingSkyscraper,
  IconCalendarCheck,
  IconCalendarClock,
  IconChevronRight,
  IconClock,
  IconEdit,
  IconFolder,
  IconFolderOpen,
  IconJetpack,
  IconMapPin,
  IconRocket,
  IconStack2,
  IconUserPlus,
  IconUsers,
} from "@tabler/icons-react";
import { CheckCircleIcon, PlayIcon } from "lucide-react";
import Link from "next/link";
import React from "react";

export function DashboardView() {
  const orgId = useOrganizationSlug();

  const organization = useOrganizationBySlug(orgId);

  const memberRole = useOrganizationMemberRole();

  const metricsQuery = api.projects.getMetrics.useQuery(
    {
      organizationId: organization.data?.id ?? "",
      memberRole,
    },
    {
      enabled: !!organization.data?.id && !!memberRole,
    },
  );

  const activityStatsQuery = useActivityStats({
    organizationId: organization.data?.id ?? "",
  });

  const timelineQuery = useProjectActivityTimeline({
    organizationId: organization.data?.id ?? "",
  });

  if (organization.isLoading) {
    return <PageLoader />;
  }

  const totalProjects = metricsQuery.data?.totalProjects ?? 0;
  const activeProjects = activityStatsQuery.data?.activeProjectsCount ?? 0;
  const approvedProjects = activityStatsQuery.data?.approvedProjectsCount ?? 0;
  const completedProjects =
    activityStatsQuery.data?.completedProjectsCount ?? 0;

  return (
    <PageWrapper>
      <div className="flex items-center justify-between">
        <div>
          <PageTitle>Dashboard</PageTitle>
          <PageDescription>
            Welcome back to {organization.data?.name}! Here&apos;s an overview
            of your organization&apos;s activity.
          </PageDescription>
        </div>
        <div className="hidden items-center gap-3 md:flex">
          <Button
            variant="outline"
            href={`/${orgId}/settings/members`}
            leftIcon={<IconUserPlus className="size-4" />}
          >
            Invite Members
          </Button>
        </div>
      </div>

      <PageContent>
        {/* Key Metrics Row */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Team Members"
            subTitle="Active Members"
            stat={metricsQuery.data?.teamMembers ?? 0}
            icon={<IconUsers className="size-5 text-muted-foreground" />}
            classNames={{
              iconBackgroundColor: "bg-gray-100",
            }}
            href={`/${orgId}/settings/members`}
          />
          <StatCard
            title="Total Projects"
            subTitle="Current Total Projects"
            stat={metricsQuery.data?.totalProjects ?? 0}
            icon={<IconFolder className="size-5 text-muted-foreground" />}
            classNames={{
              iconBackgroundColor: "bg-gray-100",
            }}
            href={`/${orgId}/projects`}
          />
          <StatCard
            title="Live Projects"
            subTitle="Projects Currently Playing"
            stat={activeProjects}
            icon={<IconFolderOpen className="size-5 text-muted-foreground" />}
            classNames={{
              iconBackgroundColor: "bg-gray-100",
            }}
            href={`/${orgId}/projects`}
          />
          <StatCard
            title="Total Slides"
            subTitle="In Active Projects"
            stat={activityStatsQuery.data?.totalSlides ?? 0}
            icon={<IconStack2 className="size-5 text-muted-foreground" />}
            classNames={{
              iconBackgroundColor: "bg-gray-100",
            }}
            href={`/${orgId}/projects`}
          />
        </div>

        {/* Activity Overview */}
        <div className="mt-8 grid gap-6 lg:grid-cols-3">
          <Card className="lg:col-span-2">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <IconActivity className="h-5 w-5 text-primary" />
                    Project Activity Dashboard
                  </CardTitle>
                  <p className="mt-1 text-sm text-muted-foreground">
                    Live insights into project lifecycle and deployment status
                  </p>
                </div>
                <Button
                  variant="ghost"
                  href={`/${orgId}/activity`}
                  size="sm"
                  rightIcon={<IconChevronRight className="size-4" />}
                >
                  View Activity
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Project Status Flow */}
              <div className="space-y-4">
                <h4 className="font-semibold tracking-wide text-muted-foreground">
                  Project Pipeline
                </h4>
                <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
                  <StatusFlowCard
                    status="editing"
                    count={activityStatsQuery.data?.editingProjectsCount ?? 0}
                    total={totalProjects}
                    icon={IconEdit}
                    label="In Development"
                    color="gray"
                    description="Projects being created"
                  />
                  <StatusFlowCard
                    status="approved"
                    count={approvedProjects}
                    total={totalProjects}
                    icon={CheckCircleIcon}
                    label="Ready to Start"
                    color="blue"
                    description="Approved for launch"
                  />
                  <StatusFlowCard
                    status="active"
                    count={activeProjects}
                    total={totalProjects}
                    icon={PlayIcon}
                    label="Live & Active"
                    color="emerald"
                    description="Currently playing"
                  />
                  <StatusFlowCard
                    status="completed"
                    count={completedProjects}
                    total={totalProjects}
                    icon={IconCalendarCheck}
                    label="Completed"
                    color="orange"
                    description="Successfully finished"
                  />
                </div>
              </div>

              <Separator />
              {/* Recent Activity Summary */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-semibold">Recent Activity</h4>
                  <Badge className="flex items-center gap-1 rounded-lg border border-gray-200 bg-gray-50 px-3 py-1.5 text-xs font-medium text-gray-700 shadow-none">
                    <IconClock className="size-3.5" />
                    <span>Last 30 days</span>
                  </Badge>
                </div>
                <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
                  {/* Upcoming Project Launches */}
                  <div className="rounded-lg border bg-gradient-to-r from-indigo-50 to-indigo-100 p-3 dark:from-indigo-950/30 dark:to-indigo-900/30">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <IconJetpack className="h-4 w-4 text-indigo-600" />
                        <span className="text-sm font-medium">
                          Upcoming Launches
                        </span>
                      </div>
                      <span className="text-lg font-bold text-indigo-600">
                        {timelineQuery.data?.recentActivity?.filter(
                          (p) =>
                            p.status === "approved" &&
                            p.startDate &&
                            new Date(p.startDate) > new Date() &&
                            new Date(p.startDate) <
                              new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
                        ).length ?? 0}
                      </span>
                    </div>
                  </div>

                  <div className="rounded-lg border border-gray-200 bg-gradient-to-r from-emerald-50 to-emerald-100 p-3 dark:from-emerald-950/30 dark:to-emerald-900/30">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <PlayIcon className="h-4 w-4 text-emerald-600" />
                        <span className="text-sm font-medium">
                          Active Deployments
                        </span>
                      </div>
                      <span className="text-lg font-bold text-emerald-600">
                        {timelineQuery.data?.activeDeployments ?? 0}
                      </span>
                    </div>
                  </div>

                  {/* New Activity Stats */}
                  <div className="rounded-lg border bg-gradient-to-r from-blue-50 to-blue-100 p-3 dark:from-blue-950/30 dark:to-blue-900/30">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <IconCalendarClock className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-medium">
                          New Projects
                        </span>
                      </div>
                      <span className="text-lg font-bold text-blue-600">
                        {timelineQuery.data?.recentActivity?.filter(
                          (p) =>
                            new Date(p.createdAt) >
                            new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                        ).length ?? 0}
                      </span>
                    </div>
                  </div>

                  {/* Recently Completed Projects */}
                  <div className="rounded-lg border bg-gradient-to-r from-orange-50 to-orange-100 p-3 dark:from-orange-950/30 dark:to-orange-900/30">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <IconCalendarCheck className="h-4 w-4 text-orange-600" />
                        <span className="text-sm font-medium">
                          Recently Completed
                        </span>
                      </div>
                      <span className="text-lg font-bold text-orange-600">
                        {timelineQuery.data?.recentActivity?.filter(
                          (p) =>
                            p.status === "completed" &&
                            new Date(p.endDate) >
                              new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                        ).length ?? 0}
                      </span>
                    </div>
                  </div>

                  <div className="rounded-lg border bg-gradient-to-r from-amber-50 to-amber-100 p-3 dark:from-amber-950/30 dark:to-amber-900/30">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <IconMapPin className="h-4 w-4 text-amber-600" />
                        <span className="text-sm font-medium">
                          Location Deployments
                        </span>
                      </div>
                      <span className="text-lg font-bold text-amber-600">
                        {activityStatsQuery.data?.totalLocationDeployments ?? 0}
                      </span>
                    </div>
                  </div>

                  <div className="rounded-lg border bg-gradient-to-r from-primary/5 to-primary/10 p-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <IconStack2 className="h-4 w-4 text-primary" />
                        <span className="text-sm font-medium">New Slides</span>
                      </div>
                      <span className="text-lg font-bold text-primary">
                        {timelineQuery.data?.slidesAddedThisMonth ?? 0}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <IconBuildingSkyscraper className="h-5 w-5 text-primary" />
                    Deployment Insights
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Location coverage and deployment efficiency
                  </p>
                </div>
                <div>
                  <Button
                    variant="ghost"
                    href={`/${orgId}/locations`}
                    size="sm"
                    rightIcon={<IconChevronRight className="size-4" />}
                  >
                    View Locations
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-3">
                <div className="rounded-lg border bg-gradient-to-r from-primary/5 to-primary/10 p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="rounded-lg bg-primary/10 p-2">
                        <IconMapPin className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <p className="font-semibold">Active Locations</p>
                        <p className="text-sm text-muted-foreground">
                          {activityStatsQuery.data?.activeLocationsCount ?? 0}{" "}
                          of {activityStatsQuery.data?.totalLocations ?? 0}{" "}
                          locations
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-2xl font-bold text-primary">
                        {activityStatsQuery.data?.locationUtilizationRate ?? 0}%
                      </p>
                      <p className="text-xs text-muted-foreground">coverage</p>
                    </div>
                  </div>
                </div>

                <div className="rounded-lg border bg-gradient-to-r from-blue-50 to-blue-100 p-4 dark:from-blue-950/30 dark:to-blue-900/30">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="rounded-lg bg-blue-500/20 p-2">
                        <IconBuildingSkyscraper className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                        <p className="font-semibold">Active Sublocations</p>
                        <p className="text-sm text-muted-foreground">
                          {activityStatsQuery.data?.activeSublocationsCount ??
                            0}{" "}
                          of {activityStatsQuery.data?.totalSublocations ?? 0}{" "}
                          zones
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {activityStatsQuery.data?.sublocationUtilizationRate ??
                          0}
                        %
                      </p>
                      <p className="text-xs text-muted-foreground">active</p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div className="rounded-lg border bg-card p-3">
                    <div className="text-center">
                      <p className="text-lg font-bold">
                        {activityStatsQuery.data?.averageLocationsPerProject ??
                          0}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Avg. locations/project
                      </p>
                    </div>
                  </div>
                  <div className="rounded-lg border bg-card p-3">
                    <div className="text-center">
                      <p className="text-lg font-bold">
                        {activityStatsQuery.data?.deploymentEfficiency ?? 0}%
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Deployment rate
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-3 grid grid-cols-2 gap-3">
                <div className="rounded-lg border bg-card p-3">
                  <div className="text-center">
                    <p className="text-lg font-bold text-emerald-600">
                      {activityStatsQuery.data?.newDeploymentsThisMonth ?? 0}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      New deployments this month
                    </p>
                  </div>
                </div>
                <div className="rounded-lg border bg-card p-3">
                  <div className="text-center">
                    <p className="text-lg font-bold text-amber-600">
                      {activityStatsQuery.data?.deploymentsEndingSoon ?? 0}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Ending in next 7 days
                    </p>
                  </div>
                </div>
              </div>

              {/* <Button
                variant="outline"
                className="w-full"
                href={`/${orgId}/locations`}
                leftIcon={<IconMapPin className="h-4 w-4" />}
              >
                Manage Locations
              </Button> */}
            </CardContent>
          </Card>
        </div>

        {/* Recent Projects & Quick Actions */}
        <div className="mt-8 grid gap-6 lg:grid-cols-3">
          <Card className="lg:col-span-2">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <IconCalendarClock className="h-5 w-5 text-primary" />
                  Recent Projects
                </CardTitle>
                <Button
                  variant="ghost"
                  href={`/${orgId}/projects`}
                  size="sm"
                  rightIcon={<IconChevronRight className="size-4" />}
                >
                  View All
                </Button>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              {metricsQuery.data?.recentProjects &&
              metricsQuery.data.recentProjects.length > 0 ? (
                <div className="divide-y divide-border">
                  {metricsQuery.data.recentProjects.map((project, index) => (
                    <Link
                      href={`/${orgId}/projects/${project.id}`}
                      key={project.id}
                      className="block"
                    >
                      <div className="group px-6 py-3 transition-all duration-200 hover:bg-muted/40">
                        <div className="flex items-center gap-4">
                          <div className="flex size-7 items-center justify-center rounded-md bg-primary/10 transition-colors group-hover:bg-primary/15">
                            <IconFolder className="size-4 text-primary" />
                          </div>
                          <div className="min-w-0 flex-1">
                            <div className="flex items-center gap-2">
                              <h3 className="truncate text-sm font-medium text-foreground transition-colors group-hover:text-primary">
                                {project.name}
                              </h3>
                              <ProjectStatusBadge
                                status={project.status as ProjectStatus}
                                className={
                                  "h-6 rounded-md px-2 text-xs font-medium"
                                }
                              />
                            </div>
                            <div className="mt-1 flex items-center gap-3 text-xs text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <IconStack2 className="h-3 w-3" />
                                <span>{project._count.slides} slides</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <IconClock className="h-3 w-3" />
                                <span>
                                  Last updated{" "}
                                  {formatDateRelative(project.updatedAt)}
                                </span>
                              </div>
                            </div>
                          </div>
                          <IconChevronRight className="h-3.5 w-3.5 text-muted-foreground transition-all group-hover:translate-x-0.5 group-hover:text-foreground" />
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              ) : (
                <div className="p-8 text-center">
                  <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-muted/60">
                    <IconFolder className="h-6 w-6 text-muted-foreground/70" />
                  </div>
                  <h3 className="mt-3 font-medium">No recent projects</h3>
                  <p className="mt-1 text-muted-foreground">
                    Get started by creating your first project.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          <QuickActionsCard orgId={orgId} memberRole={memberRole} />
        </div>
      </PageContent>
    </PageWrapper>
  );
}

function QuickActionsCard({
  orgId,
  memberRole,
}: {
  orgId: string;
  memberRole: Role;
}) {
  const actions = [
    {
      title: "Invite Team Members",
      description: "Add new members to your organization",
      href: `/${orgId}/settings/members`,
      icon: IconUserPlus,
      color: "bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400",
      disabled: memberRole !== "admin",
    },
    {
      title: "Manage Locations",
      description: "Set up and organize your deployment locations",
      href: `/${orgId}/locations`,
      icon: IconMapPin,
      color:
        "bg-emerald-100 dark:bg-emerald-900 text-emerald-600 dark:text-emerald-400",
      disabled: memberRole !== "admin",
    },
    {
      title: "View All Projects",
      description: "Browse and manage all organization projects",
      href: `/${orgId}/projects`,
      icon: IconFolder,
      color:
        "bg-amber-100 dark:bg-amber-900 text-amber-600 dark:text-amber-400",
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <IconRocket className="h-5 w-5 text-primary" />
          Quick Actions
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Common tasks to get you started
        </p>
      </CardHeader>
      <CardContent className="space-y-2">
        {actions.map((action, index) => (
          <div key={index}>
            <Link
              href={action.href}
              className={action.disabled ? "hidden" : ""}
            >
              <div className="group cursor-pointer rounded-lg border border-border px-4 py-3 transition-all hover:border-primary/30 hover:bg-muted/50">
                <div className="flex items-center gap-4">
                  <div className={`rounded-lg p-2.5 ${action.color}`}>
                    <action.icon className="h-4 w-4" />
                  </div>
                  <div className="min-w-0 flex-1 space-y-1">
                    <p className="font-medium transition-colors group-hover:text-primary">
                      {action.title}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {action.description}
                    </p>
                  </div>
                  <IconChevronRight className="h-4 w-4 text-muted-foreground transition-colors group-hover:text-foreground" />
                </div>
              </div>
            </Link>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}

interface StatusFlowCardProps {
  status: string;
  count: number;
  total: number;
  icon: React.ElementType;
  label: string;
  color: "amber" | "blue" | "emerald" | "green" | "orange" | "gray";
  description: string;
}

function StatusFlowCard({
  count,
  total,
  icon: Icon,
  label,
  color,
  description,
}: StatusFlowCardProps) {
  const percentage = total > 0 ? Math.round((count / total) * 100) : 0;

  const colorClasses = {
    amber: "bg-amber-50 border-amber-200 text-amber-700",
    blue: "bg-blue-50 border-blue-200 text-blue-700",
    emerald: "bg-emerald-50 border-emerald-200 text-emerald-700",
    green: "bg-green-50 border-green-200 text-green-700",
    orange: "bg-orange-50 border-orange-200 text-orange-700",
    gray: "bg-gray-50 border-gray-200 text-gray-700",
  };

  const iconColorClasses = {
    amber: "text-amber-600",
    blue: "text-blue-600",
    emerald: "text-emerald-600",
    green: "text-green-600",
    orange: "text-orange-600",
    gray: "text-gray-600",
  };

  return (
    <div className={`rounded-lg border p-4 ${colorClasses[color]}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Icon className={`h-4 w-4 ${iconColorClasses[color]}`} />
          <span className="text-sm font-medium">{label}</span>
        </div>
        <span className="text-lg font-bold">{count}</span>
      </div>
      <div className="mt-2">
        <Progress value={percentage} className="h-1" />
        <div className="flex items-center justify-between gap-2">
          <p className="mt-1 text-xs">{description}</p>
          <p className="text-xs text-muted-foreground">{percentage}%</p>
        </div>
      </div>
    </div>
  );
}
