import { useOrganizationMemberRole } from "@/queries/organization.queries";
import type { Role } from "@/types/organization.types";
import { ReactNode } from "react";

interface PermissionGuardProps {
  children: ReactNode;
  fallback?: ReactNode;
  allowedRoles?: Role[];
}

export function PermissionGuard({
  children,
  fallback = null,
  allowedRoles = [],
}: PermissionGuardProps) {
  const userRole = useOrganizationMemberRole();

  if (allowedRoles.length === 0) return <>{children}</>;

  const hasPermission = allowedRoles.includes(userRole as Role);

  return hasPermission ? <>{children}</> : <>{fallback}</>;
}
