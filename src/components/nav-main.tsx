"use client";

import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { useOrganizationSlug } from "@/hooks/use-organization-slug";
import {
  IconActivity,
  IconDashboard,
  IconFolder,
  IconLocation,
  IconSettings,
} from "@tabler/icons-react";
import { CheckCircle } from "lucide-react";
import Link from "next/link";
import { usePathname, useSelectedLayoutSegment } from "next/navigation";

const menuItems = [
  {
    path: "dashboard",
    name: "Dashboard",
    icon: <IconDashboard size={18} />,
  },
  {
    path: "projects",
    name: "Projects",
    icon: <IconFolder size={18} />,
  },
  {
    path: "locations",
    name: "Locations",
    icon: <IconLocation size={18} />,
  },
  {
    path: "approvals",
    name: "Approvals",
    icon: <CheckCircle size={18} />,
  },
  {
    path: "activity",
    name: "Activity Monitor",
    icon: <IconActivity size={18} />,
  },
  {
    path: "settings",
    name: "Settings",
    icon: <IconSettings size={18} />,
  },
];

export function NavMain() {
  const slug = useOrganizationSlug();
  const segment = useSelectedLayoutSegment();
  const pathname = usePathname();

  function isActive({ path, name }: { path: string; name: string }) {
    return segment === name.toLowerCase() || pathname === `/${slug}/${path}`;
  }

  return (
    <SidebarGroup>
      <SidebarMenu>
        {menuItems.map((item) => (
          <Link key={item.path} href={`/${slug}/${item.path}`}>
            <SidebarMenuItem>
              <SidebarMenuButton
                isActive={isActive({
                  path: item.path,
                  name: item.name,
                })}
                className="relative h-9 rounded-md border border-sidebar px-3 py-2 text-sm font-medium transition-all duration-200 ease-in-out data-[active=true]:border data-[active=true]:border-gray-200 data-[active=true]:bg-white data-[active=true]:shadow-xs"
              >
                {item.icon}
                <span>{item.name}</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </Link>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  );
}
