"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  type DialogProps,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useLocationUpdateMutation } from "@/queries/location.queries";
import { LocationUpdateSchema } from "@/schemas/location.schemas";
import type { LocationOutput } from "@/types/location.types";
import { states } from "@/utils/states";
import { zodResolver } from "@hookform/resolvers/zod";
import { Controller, useForm } from "react-hook-form";
import { z } from "zod";

type FormData = z.infer<typeof LocationUpdateSchema>;

interface Props extends DialogProps {
  locationId: string;
  location: LocationOutput;
  onClose: () => void;
}

export function LocationEditDialog({ location, open, onClose }: Props) {
  const updateLocationMutation = useLocationUpdateMutation();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    control,
    reset,
  } = useForm<FormData>({
    resolver: zodResolver(LocationUpdateSchema),
    values: location
      ? {
          id: location.id,
          name: location.name,
          address: location.address,
          city: location.city,
          state: states.find((state) => state.code === location.state)?.name,
          country: location.country,
          postalCode: location.postalCode,
          organizationId: location.organizationId,
        }
      : undefined,
  });

  const closeModal = () => {
    reset();
    onClose();
  };

  async function onSubmit(data: FormData) {
    try {
      await updateLocationMutation.mutateAsync({
        ...data,
        state:
          states.find((state) => state.name === data.state)?.code ?? data.state,
      });
      closeModal();
    } catch (error) {
      // Error is handled by the mutation
      console.error("Error updating location: ", error);
    }
  }

  return (
    <Dialog open={open} onOpenChange={closeModal}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle>Edit location</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="mt-3">
          <div className="space-y-6">
            <Input
              label="Location name"
              placeholder='e.g. "Main Office"'
              {...register("name")}
              allowAutoComplete={false}
              error={errors.name !== undefined}
              errorMessage={errors?.name?.message}
            />

            <Input
              label="Address"
              placeholder="123 Main St"
              {...register("address")}
              allowAutoComplete={false}
              error={errors.address !== undefined}
              errorMessage={errors?.address?.message}
            />

            <div className="grid grid-cols-2 gap-4">
              <Input
                label="City"
                placeholder="Havana"
                {...register("city")}
                allowAutoComplete={false}
                error={errors.city !== undefined}
                errorMessage={errors?.city?.message}
              />

              <Controller
                name="state"
                control={control}
                render={({ field: { value, onChange, ...fieldProps } }) => (
                  <div>
                    <Label htmlFor="state">State</Label>
                    <div className="mt-[5px]">
                      <Select
                        value={value}
                        onValueChange={onChange}
                        {...fieldProps}
                        defaultValue={value}
                      >
                        <SelectTrigger className="w-full" data-size="sm">
                          <SelectValue placeholder="Select a state" />
                        </SelectTrigger>
                        <SelectContent>
                          {states.map((state) => (
                            <SelectItem key={state.code} value={state.name}>
                              {state.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <Input
                label="Postal code"
                placeholder="12345"
                {...register("postalCode")}
                allowAutoComplete={false}
                error={errors.postalCode !== undefined}
                errorMessage={errors?.postalCode?.message}
              />
            </div>
          </div>

          <div className="mt-8 flex justify-end gap-3">
            <Button type="button" variant="outline" onClick={closeModal}>
              Cancel
            </Button>
            <Button type="submit" loading={isSubmitting}>
              Update location
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
