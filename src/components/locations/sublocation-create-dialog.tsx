"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  type DialogProps,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useSubLocationCreateMutation } from "@/queries/location.queries";
import { SubLocationCreateSchema } from "@/schemas/location.schemas";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

type FormData = z.infer<typeof SubLocationCreateSchema>;

interface Props extends DialogProps {
  locationId: string;
  onClose: () => void;
}

export function SubLocationCreateDialog({ locationId, open, onClose }: Props) {
  const createSubLocationMutation = useSubLocationCreateMutation();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<FormData>({
    resolver: zodResolver(SubLocationCreateSchema),
    defaultValues: {
      locationId,
    },
  });

  const closeModal = () => {
    reset();
    onClose();
  };

  async function onSubmit(data: FormData) {
    try {
      await createSubLocationMutation.mutateAsync(data);
      closeModal();
    } catch (error) {
      // Error is handled by the mutation
      console.error("Error creating sublocation:", error);
    }
  }

  return (
    <Dialog open={open} onOpenChange={closeModal}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create a new sublocation</DialogTitle>

          <DialogDescription>
            A sublocation is a specific area within a location, such as a
            building or floor. e.g. &quot;Building A&quot; or &quot;Floor
            2&quot;.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-6">
            <Input
              label="Sublocation name"
              {...register("name")}
              allowAutoComplete={false}
              error={errors.name !== undefined}
              errorMessage={errors?.name?.message}
              placeholder="e.g. Building A, Floor 2, Meeting Room 1"
            />
          </div>

          <div className="mt-8 flex justify-end gap-3">
            <Button type="button" variant="outline" onClick={closeModal}>
              Close
            </Button>
            <Button type="submit" loading={isSubmitting}>
              Create sublocation
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
