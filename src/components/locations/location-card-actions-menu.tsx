"use client";

import { LocationEditDialog } from "@/components/locations/location-edit-dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { DeleteDialog } from "@/components/ui/delete-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useDialog } from "@/hooks/use-dialog";
import { useLocationDeleteMutation } from "@/queries/location.queries";
import type { LocationOutput } from "@/types/location.types";
import { IconDots, IconPencil, IconTrash } from "@tabler/icons-react";

interface Props {
  disabled?: boolean;
  locationId: string;
  location: LocationOutput;
}

export function LocationCardActionsMenu({
  disabled,
  locationId,
  location,
}: Props) {
  const [openDeleteDialog, openDeleteDialogHandlers] = useDialog();
  const [openEditDialog, openEditDialogHandlers] = useDialog();
  const deleteLocationMutation = useLocationDeleteMutation();

  function onOpenDeleteDialog(e: React.SyntheticEvent) {
    e.stopPropagation();
    openDeleteDialogHandlers.open();
  }

  function onOpenEditDialog(e: React.SyntheticEvent) {
    e.stopPropagation();
    openEditDialogHandlers.open();
  }

  async function onDelete() {
    try {
      await deleteLocationMutation.mutateAsync({ id: locationId });
      openDeleteDialogHandlers.close();
    } catch (error) {
      // Error is handled by the mutation
      console.log("Error deleting location: ", error);
    }
  }

  return (
    <div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="size-7 text-gray-400 data-[state=open]:bg-accent data-[state=open]:text-gray-900"
            title="Open location actions menu"
          >
            <IconDots size={18} />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={onOpenEditDialog}>
            <IconPencil className="size-4" />
            <span>Edit</span>
          </DropdownMenuItem>

          <DropdownMenuItem
            className="text-red-500! hover:bg-red-500/5!"
            onClick={onOpenDeleteDialog}
            disabled={disabled}
          >
            <IconTrash className="size-4 text-red-500" />
            <span>Delete</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <LocationEditDialog
        location={location}
        locationId={locationId}
        open={openEditDialog}
        onClose={openEditDialogHandlers.close}
      />

      <DeleteDialog
        title="Location"
        open={openDeleteDialog}
        onClose={openDeleteDialogHandlers.close}
        onDelete={onDelete}
        loading={deleteLocationMutation.isPending}
      />
    </div>
  );
}
