"use client";

import { SubLocationCreateDialog } from "@/components/locations/sublocation-create-dialog";
import { SubLocationEditDialog } from "@/components/locations/sublocation-edit-dialog";
import { Button } from "@/components/ui/button";
import { DeleteDialog } from "@/components/ui/delete-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ScrollArea } from "@/components/ui/scroll-area";
import { env } from "@/env";
import { useClipboard } from "@/hooks/use-clipboard";
import { useDialog } from "@/hooks/use-dialog";
import { cn } from "@/libs/utils";
import { useSubLocationDeleteMutation } from "@/queries/location.queries";
import type { SubLocationOutput } from "@/types/location.types";
import { isEmpty } from "@/utils/is-empty";
import {
  IconBuilding,
  IconCopy,
  IconDots,
  IconPencil,
  IconPlus,
  IconTrash,
} from "@tabler/icons-react";
import { useEffect } from "react";
import { toast } from "sonner";

interface Props {
  locationId: string;
  sublocations: SubLocationOutput[] | undefined;
  disabled?: boolean;
}

export function SubLocationsList({
  locationId,
  sublocations,
  disabled,
}: Props) {
  const [openCreateDialog, openCreateDialogHandlers] = useDialog();

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium text-gray-900">Sublocations</h4>
        <Button
          size="xs"
          variant="secondary"
          leftIcon={<IconPlus size={14} />}
          onClick={openCreateDialogHandlers.open}
          disabled={disabled}
        >
          Add sublocation
        </Button>
      </div>

      {sublocations && !isEmpty(sublocations) ? (
        <ScrollArea
          className={cn("w-full", sublocations?.length > 3 && "h-48")}
        >
          <div className="space-y-2">
            {sublocations?.map((subLocation) => (
              <SubLocationItem
                key={subLocation?.id}
                subLocation={subLocation}
                disabled={disabled}
              />
            ))}
          </div>
        </ScrollArea>
      ) : (
        <div className="flex h-48 flex-col items-center justify-center rounded-lg border border-dashed border-gray-300 p-6 text-center">
          <IconBuilding className="mx-auto h-8 w-8 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">
            No sublocations
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating a new sublocation.
          </p>
        </div>
      )}

      <SubLocationCreateDialog
        locationId={locationId}
        open={openCreateDialog}
        onClose={openCreateDialogHandlers.close}
      />
    </div>
  );
}

interface SubLocationItemProps {
  subLocation: SubLocationOutput;
  disabled?: boolean;
}

function SubLocationItem({ subLocation, disabled }: SubLocationItemProps) {
  const [openEditDialog, openEditDialogHandlers] = useDialog();
  const [openDeleteDialog, openDeleteDialogHandlers] = useDialog();
  const deleteSubLocationMutation = useSubLocationDeleteMutation();
  const { copy, copied } = useClipboard();

  // Show toast when copied becomes true
  useEffect(() => {
    if (copied) {
      toast.success("Deployment link copied to clipboard");
    }
  }, [copied]);

  async function onDelete() {
    try {
      await deleteSubLocationMutation.mutateAsync({
        id: subLocation?.id ?? "",
      });
      openDeleteDialogHandlers.close();
    } catch (error) {
      // Error is handled by the mutation
      console.error("Error deleting sublocation:", error);
    }
  }

  function onCopy() {
    const baseUrl = window?.location?.origin ?? env.NEXT_PUBLIC_APP_BASE_URL;
    const urlToCopy = `${baseUrl}/view?location=${subLocation?.location?.shortId}&sub-location=${subLocation?.shortId}`;
    copy(urlToCopy);
  }

  return (
    <>
      <div className="flex items-center justify-between rounded-lg border border-gray-200 p-3">
        <div className="flex items-center gap-3">
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100">
            <IconBuilding className="size-4 text-gray-600" />
          </div>
          <span className="text-sm font-medium text-gray-900">
            {subLocation?.name}
          </span>
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="size-7 text-gray-400 data-[state=open]:bg-accent data-[state=open]:text-gray-900"
              title="Open sublocation actions menu"
              disabled={disabled}
            >
              <IconDots size={16} />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={openEditDialogHandlers.open}>
              <IconPencil className="size-4" />
              <span>Edit</span>
            </DropdownMenuItem>

            <DropdownMenuItem onClick={onCopy}>
              <IconCopy className="size-4" />
              <span>Copy deployment link</span>
            </DropdownMenuItem>

            <DropdownMenuItem
              className="text-red-500! hover:bg-red-500/5!"
              onClick={openDeleteDialogHandlers.open}
            >
              <IconTrash className="size-4 text-red-500" />
              <span>Delete</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <SubLocationEditDialog
        sublocation={subLocation}
        open={openEditDialog}
        onClose={openEditDialogHandlers.close}
      />

      <DeleteDialog
        title="Sublocation"
        open={openDeleteDialog}
        onClose={openDeleteDialogHandlers.close}
        onDelete={onDelete}
        loading={deleteSubLocationMutation.isPending}
      />
    </>
  );
}
