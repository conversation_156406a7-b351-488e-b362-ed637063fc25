"use client";

import { LocationCard } from "@/components/locations/location-card";
import { LocationCreateDialog } from "@/components/locations/location-create-dialog";
import { But<PERSON> } from "@/components/ui/button";
import { EmptyState } from "@/components/ui/empty-state";
import { Loader } from "@/components/ui/loader";
import {
  <PERSON><PERSON>ontent,
  PageLoader,
  PageTitle,
  PageWrapper,
} from "@/components/ui/page-structure";
import { SearchInput } from "@/components/ui/search-input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useDebouncedState } from "@/hooks/use-debounced-state";
import { useDialog } from "@/hooks/use-dialog";
import { useOrganizationSlug } from "@/hooks/use-organization-slug";
import { useInfiniteLocations } from "@/queries/location.queries";
import {
  useOrganizationBySlug,
  useOrganizationMemberRole,
} from "@/queries/organization.queries";
import type { InfiniteLocationsData } from "@/types/location.types";
import { isEmpty } from "@/utils/is-empty";
import { states } from "@/utils/states";
import { IconLocation, IconPlus } from "@tabler/icons-react";
import { useEffect, useMemo, useState } from "react";
import { useInView } from "react-intersection-observer";

function getStateNameFromCode(value: string) {
  return states.find((state) => state.code === value)?.name;
}

function getStateCodeFromName(value: string) {
  return states.find((state) => state.name === value)?.code;
}

const formatLocations = (locations?: InfiniteLocationsData) =>
  locations?.pages.flatMap((page) => page.data) ?? [];

export function LocationsPageView() {
  const slug = useOrganizationSlug();
  const [searchString, setSearchString] = useDebouncedState("", 300);
  const [selectedState, setSelectedState] = useState("");
  const [openCreateModal, openCreateModalHandlers] = useDialog();

  const { ref, inView } = useInView();

  const organization = useOrganizationBySlug(slug);
  const userRole = useOrganizationMemberRole();
  const locationsQuery = useInfiniteLocations({
    organizationId: organization?.data?.id ?? "",
    searchString,
    state: selectedState,
  });

  useEffect(() => {
    if (locationsQuery.hasNextPage && inView) {
      locationsQuery.fetchNextPage();
    }
  }, [inView, locationsQuery]);

  const data = useMemo(
    () => formatLocations(locationsQuery.data),
    [locationsQuery],
  );

  const noSearchResults =
    isEmpty(data) && (!isEmpty(searchString) || !isEmpty(selectedState));

  const isPageLoading = locationsQuery.isLoading || organization.isLoading;

  function onStateChange(value: string) {
    const stateCode = getStateCodeFromName(value) ?? "all";
    setSelectedState(stateCode);
  }

  const stateName = useMemo(() => {
    return getStateNameFromCode(selectedState) ?? "all";
  }, [selectedState]);

  return (
    <PageWrapper>
      <div className="flex items-center justify-between">
        <PageTitle>Locations</PageTitle>
        <div>
          <Button
            leftIcon={<IconPlus size={16} />}
            onClick={openCreateModalHandlers.open}
            disabled={userRole !== "admin"}
          >
            Create new location
          </Button>
        </div>
      </div>

      <PageContent className="mt-10">
        <div className="items-center gap-2 space-y-2 md:flex md:space-y-0">
          <SearchInput
            placeholder="Search locations..."
            defaultValue={searchString}
            onChange={(e) => setSearchString(e.target.value)}
            className="w-full md:w-72"
          />
          <Select value={stateName} onValueChange={onStateChange}>
            <SelectTrigger className="w-full md:w-[180px]">
              <SelectValue placeholder="Filter by state" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All States</SelectItem>
              {states.map((state) => (
                <SelectItem key={state.code} value={state.name}>
                  {state.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {isPageLoading && (
          <PageLoader className="absolute inset-0 flex items-center justify-center" />
        )}

        {!isPageLoading && (
          <>
            {!isEmpty(data) && (
              <div className="flex flex-1 flex-col gap-4 pt-6">
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 3xl:grid-cols-5">
                  {data.map((location) => (
                    <LocationCard
                      key={location.id}
                      location={{
                        ...location,
                        projects: [],
                      }}
                      userRole={userRole}
                    />
                  ))}
                </div>
              </div>
            )}
            {isEmpty(data) && !noSearchResults && (
              <div className="py-100">
                <EmptyState
                  title="No locations yet"
                  subtitle="Create a new location to get started."
                  icon={<IconLocation size={40} />}
                  actionButton={
                    <Button
                      leftIcon={<IconPlus size={16} />}
                      onClick={openCreateModalHandlers.open}
                      disabled={userRole !== "admin"}
                    >
                      Create new location
                    </Button>
                  }
                />
              </div>
            )}
            {noSearchResults && (
              <div className="py-100">
                <EmptyState
                  title="No search results"
                  subtitle="Please check the spelling or filter criteria"
                  icon={<IconLocation size={40} />}
                />
              </div>
            )}
          </>
        )}

        <div ref={ref} className="flex items-center justify-center">
          {locationsQuery.isFetchingNextPage && <Loader className="mt-5" />}
        </div>
      </PageContent>

      <LocationCreateDialog
        open={openCreateModal}
        onClose={openCreateModalHandlers.close}
      />
    </PageWrapper>
  );
}
