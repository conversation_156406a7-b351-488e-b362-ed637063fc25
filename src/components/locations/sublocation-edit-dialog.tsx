"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  type DialogProps,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useSubLocationUpdateMutation } from "@/queries/location.queries";
import { SubLocationUpdateSchema } from "@/schemas/location.schemas";
import type { SubLocationOutput } from "@/types/location.types";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

type FormData = z.infer<typeof SubLocationUpdateSchema>;

interface Props extends DialogProps {
  sublocation: SubLocationOutput;
  onClose: () => void;
}

export function SubLocationEditDialog({ sublocation, open, onClose }: Props) {
  const updateSubLocationMutation = useSubLocationUpdateMutation();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<FormData>({
    resolver: zodResolver(SubLocationUpdateSchema),
    values: sublocation
      ? {
          id: sublocation?.id,
          name: sublocation?.name,
          locationId: sublocation?.location?.id,
        }
      : undefined,
  });

  const closeModal = () => {
    reset();
    onClose();
  };

  async function onSubmit(data: FormData) {
    try {
      await updateSubLocationMutation.mutateAsync(data);
      closeModal();
    } catch (error) {
      // Error is handled by the mutation
      console.error("Error updating sublocation:", error);
    }
  }

  return (
    <Dialog open={open} onOpenChange={closeModal}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit sublocation</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="mt-3">
          <div className="space-y-6">
            <Input
              label="Sublocation name"
              {...register("name")}
              allowAutoComplete={false}
              error={errors.name !== undefined}
              errorMessage={errors?.name?.message}
              placeholder="e.g. Building A, Floor 2, Meeting Room 1"
            />
          </div>

          <div className="mt-8 flex justify-end gap-3">
            <Button type="button" variant="outline" onClick={closeModal}>
              Close
            </Button>
            <Button type="submit" loading={isSubmitting}>
              Update sublocation
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
