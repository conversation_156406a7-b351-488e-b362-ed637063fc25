"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  type DialogProps,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useOrganizationSlug } from "@/hooks/use-organization-slug";
import { useLocationCreateMutation } from "@/queries/location.queries";
import { useOrganizationBySlug } from "@/queries/organization.queries";
import { LocationCreateSchema } from "@/schemas/location.schemas";
import { states } from "@/utils/states";
import { zodResolver } from "@hookform/resolvers/zod";
import { Controller, useForm } from "react-hook-form";
import { z } from "zod";

type FormData = z.infer<typeof LocationCreateSchema>;

interface Props extends DialogProps {
  onClose: () => void;
}

export function LocationCreateDialog({ open, onClose }: Props) {
  const slug = useOrganizationSlug();
  const organization = useOrganizationBySlug(slug);
  const createLocationMutation = useLocationCreateMutation();

  const {
    register,
    handleSubmit,
    formState: { errors },
    control,
    reset,
  } = useForm<FormData>({
    resolver: zodResolver(LocationCreateSchema),
    defaultValues: {
      country: "United States", // Default country
      organizationId: organization?.data?.id ?? "",
    },
  });

  const closeModal = () => {
    reset();
    onClose();
  };

  async function onSubmit(data: FormData) {
    if (!organization?.data?.id) return;

    try {
      const stateCode = states.find((state) => state.name === data.state)?.code;
      await createLocationMutation.mutateAsync({
        ...data,
        state: stateCode ?? data.state,
        organizationId: organization.data.id,
      });
      closeModal();
    } catch (error) {
      // Error is handled by the mutation
      console.error("Error creating location: ", error);
    }
  }

  return (
    <Dialog open={open} onOpenChange={closeModal}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle>Create a new location</DialogTitle>

          <DialogDescription>
            Create a new location for where your projects will be located. These
            locations will be assigned to specific projects for viewing slides.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-6">
            <Input
              label="Location name"
              placeholder='e.g. "Main Office"'
              {...register("name")}
              allowAutoComplete={false}
              error={errors.name !== undefined}
              errorMessage={errors?.name?.message}
            />

            <Input
              label="Address"
              {...register("address")}
              placeholder="123 Main St"
              error={errors.address !== undefined}
              errorMessage={errors?.address?.message}
            />

            <div className="grid grid-cols-2 gap-4">
              <Input
                label="City"
                {...register("city")}
                placeholder="Havana"
                error={errors.city !== undefined}
                errorMessage={errors?.city?.message}
              />

              <Controller
                name="state"
                control={control}
                render={({ field: { value, onChange, ...fieldProps } }) => (
                  <div>
                    <Label htmlFor="state">State</Label>
                    <div className="mt-[5px]">
                      <Select
                        value={value}
                        onValueChange={onChange}
                        {...fieldProps}
                      >
                        <SelectTrigger className="w-full" data-size="sm">
                          <SelectValue placeholder="Select a state" />
                        </SelectTrigger>
                        <SelectContent>
                          {states.map((state) => (
                            <SelectItem key={state.code} value={state.name}>
                              {state.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <Input
                label="Postal Code"
                placeholder="12345"
                {...register("postalCode")}
                error={errors.postalCode !== undefined}
                errorMessage={errors?.postalCode?.message}
              />
            </div>
          </div>

          <DialogFooter className="mt-7">
            <Button variant="outline" onClick={closeModal} type="button">
              Close
            </Button>
            <Button loading={createLocationMutation.isPending} type="submit">
              Create location
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
