"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>con<PERSON>elector,
  <PERSON>con<PERSON><PERSON>,
  IconUserCircle,
} from "@tabler/icons-react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { Skeleton } from "@/components/ui/skeleton";
import { useLocalStorage } from "@/hooks/use-local-storage";
import { authClient } from "@/libs/auth-client";
import { APP_ROUTES } from "@/libs/constants";
import { useUser } from "@/queries/user.queries";
import { getInitials } from "@/utils/get-initials";
import { IconSettings } from "@tabler/icons-react";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";

export function NavUser() {
  const router = useRouter();
  const params = useParams();
  const [_, __, removeSelectedOrg] = useLocalStorage("selectedOrg");
  const user = useUser();

  const slug = params.slug as string;

  if (!user?.data) {
    return <Skeleton className="h-12 w-full bg-sidebar-accent" />;
  }

  const handleLogout = async () => {
    await authClient.signOut({
      fetchOptions: {
        onSuccess: () => {
          removeSelectedOrg();
          router.push(APP_ROUTES.LOGIN);
        },
      },
    });
  };

  const isAdmin = user.data.role === "admin" || user.data.role === "superadmin";

  // Define menu items in an array for easier maintenance
  const menuItems = [
    {
      key: "settings",
      href: `/${slug}/settings`,
      icon: <IconSettings />,
      label: "Organization Settings",
      show: true,
    },
    {
      key: "account",
      href: `/${slug}/account`,
      icon: <IconUser />,
      label: "Manage account",
      show: true,
    },
    {
      key: "admin",
      href: "/admin",
      icon: <IconUserCircle />,
      label: "Admin",
      show: isAdmin,
    },
    {
      key: "logout",
      onClick: handleLogout,
      icon: <IconLogout />,
      label: "Log out",
      show: true,
    },
  ];

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <Avatar className="h-8 w-8 rounded-lg">
                <AvatarImage src={user.data.image ?? ""} alt={user.data.name} />
                <AvatarFallback className="rounded-lg">
                  {user?.data?.firstName?.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">{user.data.name}</span>
                <span className="truncate text-xs">{user.data.email}</span>
              </div>
              <IconSelector className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg">
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                <Avatar className="h-8 w-8 rounded-lg">
                  <AvatarImage
                    src={user.data.image ?? ""}
                    alt={user.data.name}
                  />
                  <AvatarFallback className="rounded-lg">
                    {getInitials(user.data.name)}
                  </AvatarFallback>
                </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">
                    {user.data.name}
                  </span>
                  <span className="truncate text-xs">{user.data.email}</span>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              {menuItems
                .filter((item) => item.show && item.key !== "logout")
                .map((item) =>
                  item.href ? (
                    <Link href={item.href} key={item.key}>
                      <DropdownMenuItem>
                        {item.icon}
                        {item.label}
                      </DropdownMenuItem>
                    </Link>
                  ) : (
                    <DropdownMenuItem key={item.key} onClick={item.onClick}>
                      {item.icon}
                      {item.label}
                    </DropdownMenuItem>
                  ),
                )}
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            {menuItems
              .filter((item) => item.key === "logout" && item.show)
              .map((item) => (
                <DropdownMenuItem key={item.key} onClick={item.onClick}>
                  {item.icon}
                  {item.label}
                </DropdownMenuItem>
              ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
