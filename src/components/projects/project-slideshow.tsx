"use client";

import { env } from "@/env";
import { useWindow } from "@/hooks/use-window";
import { api } from "@/trpc/react";
import type { SlideshowSlides } from "@/types/slide.types";
import NextImage from "next/image";
import { useEffect, useMemo, useRef, useState } from "react";

interface Props {
  initialSlides: SlideshowSlides;
  locationId: string;
  subLocationId?: string;
  autoPlayInterval?: number;
  kioskMode?: boolean;
}

export function ProjectSlideshow({
  initialSlides,
  locationId,
  subLocationId,
  autoPlayInterval = 10000,
  kioskMode = false,
}: Props) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const window = useWindow();

  // Get slides from active projects at the specified location every 6 hours
  const slidesData = api.slides.getForLocation.useQuery(
    {
      locationId,
      subLocationId,
    },
    {
      initialData: initialSlides,
      refetchInterval: env.NEXT_PUBLIC_REFETCH_INTERVAL,
      refetchIntervalInBackground: true,
    },
  );

  const slides = useMemo(
    () => slidesData.data?.slides ?? [],
    [slidesData.data?.slides],
  );
  const project = slidesData.data?.project;

  // Disable keyboard shortcuts and context menu in kiosk mode
  useEffect(() => {
    if (!kioskMode || !window) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      // Disable common exit keys in kiosk mode
      if (
        e.key === "F11" ||
        e.key === "Escape" ||
        (e.altKey && e.key === "Tab") ||
        (e.ctrlKey && (e.key === "w" || e.key === "q"))
      ) {
        e.preventDefault();
      }
    };

    const handleContextMenu = (e: MouseEvent) => {
      e.preventDefault(); // Disable right-click menu
    };

    window.document.addEventListener("keydown", handleKeyDown);
    window.document.addEventListener("contextmenu", handleContextMenu);

    return () => {
      window.document.removeEventListener("keydown", handleKeyDown);
      window.document.removeEventListener("contextmenu", handleContextMenu);
    };
  }, [kioskMode, window]);

  // Prevent screen sleep/idle
  useEffect(() => {
    if (!kioskMode || !window) return;

    let wakeLock: WakeLockSentinel | null = null;

    const requestWakeLock = async () => {
      try {
        if ("wakeLock" in window.navigator) {
          wakeLock = await window.navigator.wakeLock.request("screen");
        }
      } catch (err) {
        console.warn("Wake lock failed:", err);
      }
    };

    requestWakeLock();

    return () => {
      wakeLock?.release();
    };
  }, [kioskMode, window]);

  // Auto-reconnect on network issues
  useEffect(() => {
    if (!kioskMode || !window) return;

    const handleOnline = () => {
      window.location.reload();
    };

    window.addEventListener("online", handleOnline);
    return () => window.removeEventListener("online", handleOnline);
  }, [kioskMode, window]);

  // Track the current slide id
  const currentSlideId = slides[currentIndex]?.id;

  // When slides change, try to keep the same slide visible
  useEffect(() => {
    if (!slides.length) {
      setCurrentIndex(0);
      return;
    }
    // Try to find the current slide id in the new slides array
    const newIndex = slides.findIndex((s) => s.id === currentSlideId);
    if (newIndex === -1) {
      // If the current slide is gone, reset to the first slide
      setCurrentIndex(0);
    } else {
      setCurrentIndex(newIndex);
    }
    // Only run when slides array changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [slides]);

  // Auto-play functionality with per-slide/project duration
  useEffect(() => {
    if (intervalRef.current) clearTimeout(intervalRef.current);
    if (slides.length <= 1) return;

    // Get the duration for the current slide (ms)
    let durationMs = autoPlayInterval;
    if (
      slides[currentIndex]?.duration &&
      typeof slides[currentIndex].duration === "number"
    ) {
      durationMs = slides[currentIndex].duration * 1000;
    } else if (
      project?.slideDuration &&
      typeof project.slideDuration === "number"
    ) {
      durationMs = project.slideDuration * 1000;
    }

    intervalRef.current = setTimeout(() => {
      setCurrentIndex((prev) => (prev + 1) % slides.length);
    }, durationMs);

    return () => {
      if (intervalRef.current) clearTimeout(intervalRef.current);
    };
  }, [slides, slides.length, currentIndex, autoPlayInterval, project]);

  // Preload the next image to prevent hiccups during fade transitions
  useEffect(() => {
    if (slides.length < 2) return;
    const nextIndex = (currentIndex + 1) % slides.length;
    const img = new Image();
    img.src = slides[nextIndex]?.imageUrl ?? "";
  }, [currentIndex, slides]);

  if (!slides.length) {
    return (
      <div
        ref={containerRef}
        className="flex h-screen w-full items-center justify-center bg-black lg:w-screen"
        style={{ userSelect: "none" }}
      >
        <p className="text-4xl font-medium text-white">No slides to display</p>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className="relative h-screen w-full overflow-hidden bg-black lg:w-screen"
      style={{
        userSelect: "none",
        cursor: kioskMode ? "none" : "default",
      }}
    >
      {slides.map((slide, index) => (
        <div
          key={slide.id}
          className={`absolute inset-0 flex h-full w-full items-center justify-center p-16 transition-opacity duration-1000 ease-in-out lg:p-0 ${
            index === currentIndex ? "opacity-100" : "opacity-0"
          }`}
        >
          <div className="relative h-full w-full">
            <NextImage
              src={slide.imageUrl}
              alt={`Slide ${index + 1}`}
              className="h-full w-full object-contain"
              priority
              fill
              draggable={false}
            />
          </div>
        </div>
      ))}
    </div>
  );
}
