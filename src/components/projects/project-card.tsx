"use client";

import { ProjectCardActionsMenu } from "@/components/projects/project-card-actions-menu";
import { ProjectStatusBadge } from "@/components/projects/project-status-badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { PROJECT_STATUS } from "@/libs/constants";
import { cn } from "@/libs/utils";
import type { Project, ProjectStatus } from "@/types/project.types";
import { formatDate, formatDateRelative } from "@/utils/format-date";
import {
  IconCalendarWeek,
  IconClock,
  IconPencil,
  IconStack2,
} from "@tabler/icons-react";
import Link from "next/link";

export function ProjectCard({
  project,
  orgSlug,
}: {
  project: Project;
  orgSlug: string;
}) {
  const cardBorderColors = {
    [PROJECT_STATUS.ACTIVE]: "border-t-green-400",
    [PROJECT_STATUS.APPROVED]: "border-t-blue-500",
    [PROJECT_STATUS.EDITING]: "border-t-gray-300",
    [PROJECT_STATUS.PENDING_APPROVAL]: "border-t-yellow-400",
    [PROJECT_STATUS.COMPLETED]: "border-t-orange-500",
  } as const;

  const projectStatus =
    (project.status as ProjectStatus) ?? PROJECT_STATUS.EDITING;

  const isWithinFiveDays = (date: string | Date) => {
    const fiveDaysAgo = new Date();
    fiveDaysAgo.setDate(fiveDaysAgo.getDate() - 5);
    return new Date(date) >= fiveDaysAgo;
  };

  const getUpdateText = (updatedAt: string | Date) => {
    return isWithinFiveDays(updatedAt)
      ? `Updated ${formatDateRelative(updatedAt)}`
      : `Updated ${formatDate(updatedAt)}`;
  };

  const getDateText = (project: Project) => {
    if (project.status === PROJECT_STATUS.ACTIVE) {
      return `${formatDate(project.startDate)} to ${formatDate(project.endDate)}`;
    }
    if (project.status === PROJECT_STATUS.COMPLETED) {
      return `Completed on ${formatDate(project.endDate)}`;
    }
    if (project.status === PROJECT_STATUS.PENDING_APPROVAL) {
      return `Starting on ${formatDate(project.startDate)} (awaiting approval)`;
    }
    if (project.status === PROJECT_STATUS.APPROVED) {
      return `Starting on ${formatDate(project.startDate)}`;
    }
    return "Not started yet";
  };

  return (
    <Card
      className={cn(`border border-t-4 border-gray-200`, {
        [cardBorderColors[projectStatus]]: projectStatus,
      })}
    >
      <div className="p-6">
        <div className="flex items-center justify-between">
          <ProjectStatusBadge status={projectStatus} />
          <ProjectCardActionsMenu
            projectId={project.id}
            status={projectStatus}
            organizationSlug={orgSlug}
          />
        </div>

        <Link href={`/${orgSlug}/projects/${project.id}`}>
          <h3 className="mt-4 text-lg font-semibold text-gray-900">
            {project.name}
          </h3>
        </Link>

        <div className="mt-3 flex items-center justify-between">
          <div className="mr-2 flex min-w-0 flex-1 items-center gap-1">
            <IconClock className="size-4 flex-shrink-0" />
            <p className="truncate text-sm text-gray-600">
              {getUpdateText(project.updatedAt)}
            </p>
          </div>
          <div className="flex flex-shrink-0 items-center gap-1">
            <IconStack2 className="size-4" />
            <p className="text-sm text-gray-600">
              {project._count.slides} slides
            </p>
          </div>
        </div>

        <div className="mt-4 flex items-center gap-2 text-sm text-gray-600">
          <IconCalendarWeek className="h-4 w-4" />
          <span className="block max-w-xs truncate">
            {getDateText(project)}
          </span>
        </div>

        <div className="mt-4 flex items-center justify-between gap-2">
          <Button
            variant="secondary"
            leftIcon={getActionButtonIcon(project)}
            href={getActionButtonHref(project, orgSlug)}
            className="w-full"
            linkClassname="w-full"
          >
            {getActionButtonLabel(project)}
          </Button>
        </div>
      </div>
    </Card>
  );
}

/**
 * Returns the appropriate href for the action button based on the project's status.
 *
 * @param project - The project object whose status determines the button href.
 * @param orgSlug - The organization slug.
 * @returns A string representing the button href:
 *   - `/approvals` when status is `PENDING_APPROVAL`
 *   - `/projects/[id]` for all other statuses
 */
function getActionButtonHref(project: Project, orgSlug: string) {
  if (project.status === PROJECT_STATUS.PENDING_APPROVAL) {
    return `/${orgSlug}/approvals`;
  }
  return `/${orgSlug}/projects/${project.id}`;
}

/**
 * Returns the appropriate label for the action button based on the project's status.
 *
 * @param project - The project object whose status determines the button label.
 * @returns A string representing the button label:
 *   - "Review" when status is `PENDING_APPROVAL`
 *   - "Edit" when status is `EDITING`
 *   - "View" for all other statuses
 */
function getActionButtonLabel(project: Project) {
  if (project.status === PROJECT_STATUS.PENDING_APPROVAL) {
    return "Review";
  }
  if (project.status === PROJECT_STATUS.EDITING) {
    return "Edit";
  }
  return "View";
}

/**
 * Returns the appropriate action button icon for a project based on its status.
 *
 * @param project - The project object whose status determines which icon to render.
 * @returns A React element representing the action button icon:
 *   - `IconClock` when status is `PENDING_APPROVAL`
 *   - `IconPencil` when status is `EDITING`
 *   - `IconCalendarWeek` for all other statuses
 */
function getActionButtonIcon(project: Project) {
  if (project.status === PROJECT_STATUS.PENDING_APPROVAL) {
    return <IconClock size={16} />;
  }
  if (project.status === PROJECT_STATUS.EDITING) {
    return <IconPencil size={16} />;
  }
  return <IconCalendarWeek size={16} />;
}
