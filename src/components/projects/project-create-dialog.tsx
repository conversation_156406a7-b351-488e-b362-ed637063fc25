import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>alogTitle,
  type DialogProps,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useOrganizationSlug } from "@/hooks/use-organization-slug";
import type { z } from "@/libs/zod";
import { useOrganizationBySlug } from "@/queries/organization.queries";
import { useProjectAddMutation } from "@/queries/project.queries";
import { ProjectCreateSchema } from "@/schemas/project.schemas";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";

type FormData = z.infer<typeof ProjectCreateSchema>;

interface Props extends DialogProps {
  onClose: () => void;
}

export function ProjectCreateDialog({ open, onClose }: Props) {
  const slug = useOrganizationSlug();
  const organization = useOrganizationBySlug(slug);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm({
    resolver: zodResolver(ProjectCreateSchema),
  });

  const closeModal = () => {
    reset();
    onClose();
  };

  const handleFormSubmit = useProjectAddMutation();

  async function onSubmit(data: FormData) {
    console.log(data);
    await handleFormSubmit.mutateAsync({
      ...data,
      organizationId: organization?.data?.id ?? "",
    });
    closeModal();
  }

  return (
    <Dialog open={open} onOpenChange={closeModal}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle>Create a new project</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="mt-3">
          <div className="space-y-6">
            <Input
              label="Project name"
              {...register("name")}
              allowAutoComplete={false}
              error={errors.name !== undefined}
              errorMessage={errors?.name?.message}
            />
          </div>

          <DialogFooter className="mt-7">
            <Button variant="outline" onClick={closeModal} type="button">
              Close
            </Button>
            <Button loading={isSubmitting} type="submit">
              Create project
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
