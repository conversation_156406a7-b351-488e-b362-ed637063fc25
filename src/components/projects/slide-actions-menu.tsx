"use client";

import { SlidesImageUploaderUpdate } from "@/components/projects/slides-image-uploader-update";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { DeleteDialog } from "@/components/ui/delete-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useDialog } from "@/hooks/use-dialog";
import { useSlideDeleteMutation } from "@/queries/slide.queries";
import { useFileDeleteMutation } from "@/queries/storage.queries";
import type { Slide } from "@/types/slide.types";
import { IconDots, IconPencil, IconTrash } from "@tabler/icons-react";

interface Props {
  disabled?: boolean;
  slide: Slide;
  organizationId: string;
}

export function SlideActionsMenu({ disabled, slide, organizationId }: Props) {
  const [openDeleteDialog, openDeleteDialogHandlers] = useDialog();
  const [openEditDialog, openEditDialogHandlers] = useDialog();

  const deleteMutation = useSlideDeleteMutation();
  const deleteFileMutation = useFileDeleteMutation();

  function onOpenEditDialog(e: React.SyntheticEvent) {
    e.stopPropagation();
    openEditDialogHandlers.open();
  }

  function onOpenDeleteDialog(e: React.SyntheticEvent) {
    e.stopPropagation();
    openDeleteDialogHandlers.open();
  }

  async function onDelete() {
    await deleteFileMutation.mutateAsync({ fileKey: slide.objectKey });
    await deleteMutation.mutateAsync({ id: slide.id });
  }

  return (
    <div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="size-6"
            onClick={(e) => e.stopPropagation()}
            disabled={disabled}
          >
            <IconDots className="size-4" />
            <span className="sr-only">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={onOpenEditDialog} disabled={disabled}>
            <IconPencil className="mr-2 size-4" />
            Edit
          </DropdownMenuItem>
          <DropdownMenuItem
            className="text-red-500! hover:bg-red-500/5!"
            onClick={onOpenDeleteDialog}
            disabled={disabled}
          >
            <IconTrash className="size-4 text-red-500" />
            <span>Delete</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <SlidesImageUploaderUpdate
        slide={slide}
        open={openEditDialog}
        onClose={openEditDialogHandlers.close}
        organizationId={organizationId}
      />

      <DeleteDialog
        title="Slide"
        open={openDeleteDialog}
        onClose={openDeleteDialogHandlers.close}
        onDelete={onDelete}
        loading={deleteMutation.isPending || deleteFileMutation.isPending}
      />
    </div>
  );
}
