"use client";

import type { SlideshowPreviewSlide } from "@/types/slide.types";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";

interface Props {
  slides: SlideshowPreviewSlide[];
  autoPlayInterval?: number;
}

export function ProjectSlideshowPreview({
  slides,
  autoPlayInterval = 10000,
}: Props) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Auto-play functionality with per-slide duration
  useEffect(() => {
    if (intervalRef.current) clearTimeout(intervalRef.current);
    if (slides.length <= 1) return;

    let durationMs = autoPlayInterval;
    if (
      slides[currentIndex]?.duration &&
      typeof slides[currentIndex].duration === "number"
    ) {
      durationMs = slides[currentIndex].duration * 1000;
    }

    intervalRef.current = setTimeout(() => {
      setCurrentIndex((prev) => (prev + 1) % slides.length);
    }, durationMs);

    return () => {
      if (intervalRef.current) clearTimeout(intervalRef.current);
    };
  }, [slides.length, currentIndex, autoPlayInterval]);

  // Preload the next image to prevent hiccups during fade transitions
  useEffect(() => {
    if (slides.length < 2) return;
    const nextIndex = (currentIndex + 1) % slides.length;
    const img = new window.Image();
    img.src = slides[nextIndex]?.imageUrl ?? "";
  }, [currentIndex, slides]);

  if (!slides.length) {
    return (
      <div className="flex h-screen w-full items-center justify-center bg-black lg:w-screen">
        <p className="text-4xl font-medium text-white">No slides to display</p>
      </div>
    );
  }

  return (
    <div className="relative h-screen w-full overflow-hidden bg-black lg:w-screen">
      {slides.map((slide, index) => (
        <div
          key={slide.id}
          className={`absolute inset-0 flex h-full w-full items-center justify-center p-16 transition-opacity duration-1000 ease-in-out lg:p-0 ${
            index === currentIndex ? "opacity-100" : "opacity-0"
          }`}
        >
          <div className="relative h-full w-full">
            <Image
              src={slide.imageUrl}
              alt={`Slide ${index + 1}`}
              className="h-full w-full object-contain"
              priority
              fill
            />
          </div>
        </div>
      ))}
    </div>
  );
}
