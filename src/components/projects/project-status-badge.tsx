import { Badge } from "@/components/ui/badge";
import { PROJECT_STATUS } from "@/libs/constants";
import { cn } from "@/libs/utils";
import type { ProjectStatus } from "@/types/project.types";
import { snakeToSpaces } from "@/utils/snake-case-to-spaces";
import { IconCalendarCheck, IconClock, IconPencil } from "@tabler/icons-react";
import { CheckCircle, Play } from "lucide-react";

interface ProjectStatusBadgeProps {
  status: ProjectStatus;
  className?: string;
}

export function ProjectStatusBadge({
  status,
  className = "",
}: ProjectStatusBadgeProps) {
  function getProjectStatusVariant(status: ProjectStatus) {
    switch (status) {
      case PROJECT_STATUS.ACTIVE:
        return "green";
      case PROJECT_STATUS.APPROVED:
        return "blue";
      case PROJECT_STATUS.EDITING:
        return "gray";
      case PROJECT_STATUS.PENDING_APPROVAL:
        return "yellow";
      case PROJECT_STATUS.COMPLETED:
        return "orange";
      default:
        return "gray";
    }
  }

  const statusIcons = {
    [PROJECT_STATUS.ACTIVE]: <Play className="h-3.5 w-3.5" />,
    [PROJECT_STATUS.APPROVED]: <CheckCircle className="h-3.5 w-3.5" />,
    [PROJECT_STATUS.EDITING]: <IconPencil className="h-3.5 w-3.5" />,
    [PROJECT_STATUS.PENDING_APPROVAL]: <IconClock className="h-3.5 w-3.5" />,
    [PROJECT_STATUS.COMPLETED]: <IconCalendarCheck className="h-3.5 w-3.5" />,
  };

  return (
    <Badge
      className={cn("flex items-center gap-1.5 px-3 py-1.5", className)}
      variant={getProjectStatusVariant(status)}
    >
      {statusIcons[status as keyof typeof statusIcons]}
      <span className="capitalize">{snakeToSpaces(status)}</span>
    </Badge>
  );
}
