import type { Slide } from "@/types/slide.types";
import Image from "next/image";

interface SlideProps {
  slide: Slide | undefined;
}

export function SlidePreview({ slide }: SlideProps) {
  return (
    <div className="flex h-[90vh] flex-col items-center justify-center">
      <div className="flex h-full w-full flex-col items-center justify-center">
        <div className="relative h-[70vh] max-h-[800px] w-[90%]">
          {slide && (
            <Image
              src={slide.imageUrl ?? "/placeholder.svg"}
              alt={""}
              fill
              sizes="(max-width: 768px) 100vw, 1400px"
              className="object-contain"
              priority
            />
          )}
        </div>
      </div>
    </div>
  );
}
