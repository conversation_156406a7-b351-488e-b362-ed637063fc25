import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON><PERSON>ooter,
  DialogHeader,
  DialogTitle,
  type DialogProps,
} from "@/components/ui/dialog";
import { useProjectUpdateMutation } from "@/queries/project.queries";

interface Props extends DialogProps {
  onClose: () => void;
  projectId: string;
}

export function ProjectConfirmEditDialog({ open, onClose, projectId }: Props) {
  const closeModal = () => {
    onClose();
  };

  const projectUpdateMutation = useProjectUpdateMutation();

  async function handleUpdateEditStatus() {
    await projectUpdateMutation.mutateAsync({
      id: projectId,
      status: "editing",
    });
    closeModal();
  }

  return (
    <Dialog open={open} onOpenChange={closeModal}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle>Are you sure you want to edit this project?</DialogTitle>
        </DialogHeader>

        <div className="mt-3">
          <div className="space-y-6">
            <p className="text-gray-500">
              Are you sure you want to continue? Editing this project after it
              has been published will require you to publish the project again.
            </p>
          </div>

          <DialogFooter className="mt-7">
            <Button variant="outline" onClick={closeModal} type="button">
              Close
            </Button>
            <Button
              loading={projectUpdateMutation.isPending}
              onClick={handleUpdateEditStatus}
            >
              Continue
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
}
