"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  type DialogProps,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { UploadDropzoneProgress } from "@/components/ui/upload-dropzone-progress";
import { useSlideUpdateMutation } from "@/queries/slide.queries";
import { useFileDeleteMutation } from "@/queries/storage.queries";
import type { Slide } from "@/types/slide.types";
import { IMAGE_MIME_TYPE } from "@/types/utility.types";
import { compressImage } from "@/utils/compress-image";
import { createImageUploadUrl } from "@/utils/create-image-upload-url";
import { IconExclamationCircle, IconX } from "@tabler/icons-react";
import type { UploadedFile } from "better-upload/client";
import { useState } from "react";

interface Props extends DialogProps {
  onClose: () => void;
  organizationId: string;
  slide: Slide;
}

export function SlidesImageUploaderUpdate({
  onClose,
  open,
  organizationId,
  slide,
}: Props) {
  const [uploadError, setUploadError] = useState("");
  const [duration, setDuration] = useState(slide.duration ?? null);
  const [pendingFiles, setPendingFiles] = useState<UploadedFile[]>([]);
  const [isCompressing, setIsCompressing] = useState(false);

  const updateSlideMutation = useSlideUpdateMutation();
  const deleteFileMutation = useFileDeleteMutation();

  async function handleUploadComplete(files: UploadedFile[]) {
    if (files.length === 0 || !files[0]?.objectKey) {
      return;
    }
    // Store files for later processing
    setPendingFiles(files);
  }

  async function onSubmit() {
    try {
      const updateData: any = {
        id: slide.id,
        duration: duration ?? slide.duration,
      };

      // If there are pending files, include image update
      if (pendingFiles.length > 0 && pendingFiles[0]?.objectKey) {
        await deleteFileMutation.mutateAsync({ fileKey: slide.objectKey });
        updateData.imageUrl = createImageUploadUrl(pendingFiles[0].objectKey);
        updateData.objectKey = pendingFiles[0].objectKey;
      }

      await updateSlideMutation.mutateAsync(updateData);
      closeModal();
    } catch (error) {
      console.error("Error updating slide:", error);
    }
  }

  function closeModal() {
    onClose();
    setUploadError("");
    setPendingFiles([]);
  }

  return (
    <Dialog open={open} onOpenChange={closeModal}>
      <DialogContent className="sm:max-w-[550px]" hideCloseButton={false}>
        <DialogTitle>Update Slide</DialogTitle>

        <div className="mt-2 space-y-4">
          {uploadError && (
            <div className="relative mb-4 rounded-md bg-red-50 p-4">
              <div className="flex items-start justify-between">
                <div className="flex">
                  <div className="shrink-0">
                    <IconExclamationCircle
                      className="h-5 w-5 text-red-400"
                      aria-hidden="true"
                    />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">Error</h3>
                    <div className="mt-2 text-sm text-red-700">
                      <p>{uploadError}</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="absolute top-1 right-1">
                <Button
                  size="icon"
                  variant="ghost"
                  className="h-7 w-7 text-red-500 hover:bg-red-100 hover:text-red-500"
                  onClick={() => setUploadError("")}
                >
                  <IconX size={16} />
                </Button>
              </div>
            </div>
          )}
          <section>
            <Input
              label="Slide duration (seconds)"
              type="number"
              defaultValue={duration ?? ""}
              onChange={(e) => setDuration(Number(e.target.value))}
            />
          </section>
          <section>
            <UploadDropzoneProgress
              route="singleSlideUpload"
              description={{
                maxFiles: 1,
                maxFileSize: "4MB",
                fileTypes: "JPEG, PNG, GIF, WEBP, AVIF",
              }}
              accept={IMAGE_MIME_TYPE.join(",")}
              onBeforeUpload={async ({ files }) => {
                setUploadError("");
                setIsCompressing(true);
                try {
                  const compressedFiles = await Promise.all(
                    files.map(async (file) => {
                      const compressed = await compressImage(file);
                      return new File(
                        [compressed],
                        `${organizationId}-${file.name}`,
                        { type: compressed.type },
                      );
                    }),
                  );
                  setIsCompressing(false);
                  return compressedFiles;
                } catch (error) {
                  setIsCompressing(false);
                  setUploadError("Image compression failed");
                  return files.map(
                    (file) =>
                      new File([file], `${organizationId}-${file.name}`, {
                        type: file.type,
                      }),
                  );
                }
              }}
              onUploadComplete={({ files }) => {
                handleUploadComplete(files);
              }}
              onUploadError={(error) => {
                console.log("error", error);
                if (error.message) {
                  setUploadError(error.message);
                }
              }}
            />
            {isCompressing && (
              <div className="mt-2 text-center text-sm text-gray-500">
                Compressing image...
              </div>
            )}
          </section>
          <Button
            type="button"
            className="w-full"
            onClick={onSubmit}
            loading={
              deleteFileMutation.isPending || updateSlideMutation.isPending
            }
          >
            Save changes
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
