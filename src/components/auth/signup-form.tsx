"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";

import { AlertError } from "@/components/ui/alert-error";
import { AlertSuccess } from "@/components/ui/alert-success";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { env } from "@/env";
import { authClient } from "@/libs/auth-client";
import { APP_NAME, APP_ROUTES } from "@/libs/constants";
import { nanoid } from "@/libs/nanoid";
import { cn } from "@/libs/utils";
import { useUserUpdateMutation } from "@/queries/user.queries";
import { SignupSchema } from "@/schemas/auth.schemas";
import { type SignupFormData } from "@/types/auth.types";
import type { User } from "better-auth";

/**
 * Asynchronously generates an avatar image for a user by sending a POST request to the avatar API.
 *
 * @param seed - A string used to seed the avatar generation process.
 * @param uniqueIdentifier - The unique identifier of the user for whom the avatar is being generated.
 * @returns A promise that resolves to the URL of the generated avatar image.
 * @throws Will throw an error if the upload fails or the response is not OK.
 */
export const createAvatarImage = async (
  seed: string,
  uniqueIdentifier: string,
) => {
  const res = await fetch(`${env.NEXT_PUBLIC_APP_BASE_URL}/api/avatar`, {
    method: "POST",
    body: JSON.stringify({
      seed,
      key: `users/${uniqueIdentifier}/${nanoid(10)}.svg`,
    }),
  });
  // Check if the upload was successful
  if (!res.ok) {
    throw new Error(`Upload failed: ${res.status} ${res.statusText}`);
  }
  // // Get the image URL from the response
  const response = await res.json();
  // Return the image URL
  return response.url as string;
};

export function SignupForm({
  className,
  searchParams,
  ...props
}: React.ComponentPropsWithoutRef<"div"> & {
  searchParams: { [key: string]: string | string[] | undefined };
}) {
  const router = useRouter();
  const [errorMessage, setErrorMessage] = useState<string | undefined>();

  const callbackUrl = searchParams["callbackUrl"] as string;
  const email = searchParams["email"]
    ? decodeURIComponent(searchParams["email"]! as string)
    : "";
  const organization = searchParams["organization"]
    ? decodeURIComponent(searchParams["organization"]! as string)
    : "";

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: zodResolver(SignupSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email,
      password: "",
    },
  });

  const updateUserMutation = useUserUpdateMutation();

  async function onSubmit(data: SignupFormData) {
    setErrorMessage("");
    await authClient.signUp.email({
      name: `${data.firstName} ${data.lastName}`,
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email,
      password: data.password,
      fetchOptions: {
        onSuccess: async (ctx) => {
          const user = ctx.data.user as User;
          // Generate the user image
          const userImage = await createAvatarImage(data.firstName, user.id);
          // Update the user image in the database
          await updateUserMutation.mutateAsync({
            image: userImage,
          });
          if (callbackUrl) {
            return router.push(callbackUrl);
          }
          return router.push(APP_ROUTES.CREATE_ORGANIZATION);
        },
        onError: (ctx) => {
          setErrorMessage(ctx.error.message);
        },
      },
    });
  }

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card className="p-6">
        <CardHeader className="text-center">
          <CardTitle className="text-xl">Sign up</CardTitle>
          <CardDescription>
            Enter your details below to create your account.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AlertError message={errorMessage} className="mb-6" />

          <AlertSuccess className="mb-6">
            {callbackUrl && (
              <p>
                You have been invited to join{" "}
                <span className="font-bold text-gray-950">{organization}</span>{" "}
                on {APP_NAME}. Create an account to join the organization.
              </p>
            )}
          </AlertSuccess>

          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="grid gap-6">
              <div className="grid gap-6">
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    id="firstName"
                    type="text"
                    label="First name"
                    {...register("firstName")}
                    error={errors.firstName !== undefined}
                    errorMessage={errors?.firstName?.message}
                  />
                  <Input
                    id="lastName"
                    type="text"
                    label="Last name"
                    {...register("lastName")}
                    error={errors.lastName !== undefined}
                    errorMessage={errors?.lastName?.message}
                  />
                </div>
                <div className="grid">
                  <Input
                    id="email"
                    type="email"
                    label="Email"
                    {...register("email")}
                    error={errors.email !== undefined}
                    errorMessage={errors?.email?.message}
                  />
                </div>
                <div className="grid">
                  <Input
                    id="password"
                    label="Password"
                    type="password"
                    {...register("password")}
                    disabled={isSubmitting}
                    error={errors.password !== undefined}
                    errorMessage={errors?.password?.message}
                  />
                </div>
                <Button type="submit" className="w-full" loading={isSubmitting}>
                  Sign up
                </Button>
              </div>

              <div className="text-center text-sm">
                Already have an account?{" "}
                <Link
                  href="/auth/login"
                  className="underline underline-offset-4"
                >
                  Login
                </Link>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>
      <div className="mx-auto w-sm text-center text-xs text-balance text-muted-foreground [&_a]:underline [&_a]:underline-offset-4 hover:[&_a]:text-primary">
        By clicking continue, you agree to our <a href="#">Terms of Service</a>{" "}
        and <a href="#">Privacy Policy</a>.
      </div>
    </div>
  );
}
