"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";

import { AlertError } from "@/components/ui/alert-error";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { cn } from "@/libs/utils";
import { z } from "@/libs/zod";
import { useUserPasswordResetMutation } from "@/queries/user.queries";
import { IconArrowLeft } from "@tabler/icons-react";

const schema = z.object({
  email: z.string().email().min(1, "Email is required"),
});

type FormData = z.infer<typeof schema>;

type Props = {
  searchParams?: { [key: string]: string | string[] | undefined };
} & React.ComponentPropsWithoutRef<"div">;

export function ForgotPasswordForm({ className, ...props }: Props) {
  const [errorMessage, setErrorMessage] = useState<string | undefined>();
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [userEmail, setUserEmail] = useState("");

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<FormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      email: "",
    },
  });

  const passwordResetMutation = useUserPasswordResetMutation({
    onSuccess: () => {
      setShowSuccessMessage(true);
    },
    onError: (error) => {
      setErrorMessage(error.message);
    },
  });

  async function onSubmit(data: FormData) {
    setErrorMessage("");
    await passwordResetMutation.mutateAsync({ email: data.email });
  }

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card className="p-6">
        {showSuccessMessage && (
          <>
            <CardHeader className="text-center">
              <CardTitle className="text-2xl">Forgot your password?</CardTitle>
            </CardHeader>

            <CardContent className="prose text-gray-500">
              <p>
                An email is on it&apos;s way to{" "}
                <strong className="text-gray-950">{userEmail}</strong>. Follow
                the instructions to reset your password.
              </p>
              <p>
                If you don&apos;t receive an email soon, check that the email
                you entered is correct, check your spam folder or reach out to
                support if the issue persists.
              </p>
            </CardContent>
          </>
        )}
        {!showSuccessMessage && (
          <>
            <CardHeader className="text-center">
              <CardTitle className="text-2xl">Forgot your password?</CardTitle>
              <CardDescription className="text-base">
                No worries! Enter your email below and we&apos;ll send you a
                link to reset your password.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AlertError message={errorMessage} className="mb-6" />

              <form onSubmit={handleSubmit(onSubmit)}>
                <div className="grid gap-6">
                  <div className="grid gap-6">
                    <div className="grid gap-2">
                      <Input
                        id="email"
                        type="email"
                        label="Email"
                        {...register("email", {
                          onChange: (e) => setUserEmail(e.target.value),
                        })}
                        error={errors.email !== undefined}
                        errorMessage={errors?.email?.message}
                      />
                    </div>
                    <Button
                      type="submit"
                      className="w-full"
                      loading={isSubmitting}
                    >
                      Send reset email
                    </Button>
                  </div>
                </div>
              </form>
            </CardContent>
          </>
        )}
      </Card>
      <div className="mx-auto mt-2 max-w-sm">
        <Button
          href="/auth/login"
          variant="outline"
          leftIcon={<IconArrowLeft size={16} />}
        >
          Back to login page
        </Button>
      </div>
    </div>
  );
}
