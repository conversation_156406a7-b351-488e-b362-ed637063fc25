import { api } from "@/trpc/react";
import type { ApprovalsGetAllInput } from "@/types/approvals.types";
import { isEmpty } from "@/utils/is-empty";
import { toast } from "sonner";

export const useSubmitForApproval = () => {
  const utils = api.useUtils();

  return api.approvals.submitForApproval.useMutation({
    onSuccess: () => {
      toast.success("Project submitted for approval");
    },
    onError: (error) => {
      toast.error("Error", { description: error.message });
    },
    onSettled: async () => {
      await utils.projects.invalidate();
      await utils.approvals.invalidate();
    },
  });
};

export const useReviewApproval = () => {
  const utils = api.useUtils();

  return api.approvals.reviewApproval.useMutation({
    onSuccess: (data) => {
      const action = data.status === "approved" ? "approved" : "rejected";
      toast.success(`Project ${action} successfully`);
    },
    onError: (error) => {
      toast.error("Error", { description: error.message });
    },
    onSettled: async () => {
      await utils.projects.invalidate();
      await utils.approvals.invalidate();
    },
  });
};

export const useApprovals = (
  organizationId: string,
  status?: "approved" | "all" | "pending" | "rejected",
) => {
  return api.approvals.getAll.useQuery(
    { organizationId, status },
    { enabled: !!organizationId },
  );
};

export const useInfiniteApprovals = (input?: ApprovalsGetAllInput) => {
  return api.approvals.getAll.useInfiniteQuery(
    { ...input, organizationId: input?.organizationId ?? "" },
    {
      enabled: !isEmpty(input?.organizationId),
      getNextPageParam: (lastPage) => lastPage?.cursor || undefined,
    },
  );
};

export const usePendingReviews = (organizationId: string) => {
  return api.approvals.getPendingReviews.useQuery(
    { organizationId },
    { enabled: !!organizationId },
  );
};

export const useApprovalById = (id: string) => {
  return api.approvals.getById.useQuery({ id }, { enabled: !!id });
};
