"use client";

import { SlideActionsMenu } from "@/components/projects/slide-actions-menu";
import { useOrganizationSlug } from "@/hooks/use-organization-slug";
import { cn } from "@/libs/utils";
import { useOrganizationBySlug } from "@/queries/organization.queries";
import { useSlideBatchUpdateMutation } from "@/queries/slide.queries";
import type { ProjectStatus } from "@/types/project.types";
import type { Slide } from "@/types/slide.types";
import { isEqual } from "@/utils/is-equal";
import { move } from "@dnd-kit/helpers";
import { DragDropProvider } from "@dnd-kit/react";
import { useSortable } from "@dnd-kit/react/sortable";
import { generateKeyBetween } from "fractional-indexing";
import Image from "next/image";
import { useEffect, useState } from "react";
interface SlidesSidebarProps {
  slides: Slide[];
  projectStatus: ProjectStatus;
  onSelectSlideById: (id: string) => void;
  currentSlideId: string;
  isPending: boolean;
  slidesUploadingLength: number;
}

export function SlidesSidebar({
  slides,
  onSelectSlideById,
  currentSlideId,
  isPending,
  slidesUploadingLength,
  projectStatus,
}: SlidesSidebarProps) {
  const [slideItems, setSlideItems] = useState(slides);

  useEffect(() => {
    setSlideItems(slides);
  }, [slides]);

  const slug = useOrganizationSlug();
  const organization = useOrganizationBySlug(slug);

  const batchUpdateSlidesMutation = useSlideBatchUpdateMutation();

  const isProjectNotEditing = projectStatus !== "editing";

  const handleBatchUpdateSlideOrder = async (
    updatedSlides: Array<{ id: string; order: string }>,
  ) => {
    try {
      await batchUpdateSlidesMutation.mutateAsync({
        slides: updatedSlides,
      });
    } catch (error) {
      console.error("Failed to batch update slide orders:", error);
      // Optionally revert the UI on error
      setSlideItems(slides);
    }
  };

  const currentSlideIndex = slideItems.findIndex(
    (slide) => slide.id === currentSlideId,
  );

  return (
    <DragDropProvider
      onDragEnd={async (event) => {
        const movedSlideId = event.operation.source?.id as string;
        if (!movedSlideId) return;

        const previousSlides = [...slideItems];
        const newSlides = move(previousSlides, event);

        // Check if the slides have actually changed
        if (isEqual(previousSlides, newSlides)) return;

        // Get the moved slide's new position
        const movedIndex = newSlides.findIndex(
          (slide) => slide.id === movedSlideId,
        );

        // Find the before and after slides for generating the new order
        const beforeSlide = movedIndex > 0 ? newSlides[movedIndex - 1] : null;
        const afterSlide =
          movedIndex < newSlides.length - 1 ? newSlides[movedIndex + 1] : null;

        // Generate fractional index for the moved slide
        const newOrder = generateKeyBetween(
          beforeSlide?.order || null,
          afterSlide?.order || null,
        );

        setSlideItems(newSlides);

        // Only update the moved slide's order in the database
        return await handleBatchUpdateSlideOrder([
          {
            id: movedSlideId,
            order: newOrder,
          },
        ]);
      }}
    >
      <aside className="flex w-64 flex-col border-r bg-background">
        {isPending && <p>Uploading {slidesUploadingLength} slides...</p>}
        <div className="flex items-center justify-between border-b p-4">
          <h2 className="font-semibold">Slides</h2>
          <span className="text-sm">
            {slides.length > 0 && currentSlideIndex + 1 + "/" + slides.length}
          </span>
        </div>
        <div className="flex-1 overflow-y-auto p-2">
          {slideItems.map((slide, index) => (
            <SidebarSlide
              key={slide.id}
              slide={slide}
              index={index}
              organizationId={organization?.data?.id ?? ""}
              onSelectSlideById={onSelectSlideById}
              currentSlideId={currentSlideId}
              disabled={isProjectNotEditing}
            />
          ))}
          {isPending &&
            Array.from({ length: slidesUploadingLength }).map((_, index) => (
              <div
                key={index}
                className={cn(
                  "mb-2 cursor-pointer rounded-md border-2 border-primary bg-primary/5 p-1 transition-colors hover:bg-gray-100",
                )}
              >
                <div className="flex items-center justify-between gap-2"></div>
                <div className="relative mt-2 h-32 w-full overflow-hidden rounded bg-muted">
                  <div className="h-full w-full bg-gray-50" />
                </div>
              </div>
            ))}
        </div>
      </aside>
    </DragDropProvider>
  );
}

function SidebarSlide({
  slide,
  index,
  currentSlideId,
  onSelectSlideById,
  organizationId,
  disabled = false,
}: {
  slide: Slide;
  disabled?: boolean;
  index: number;
  organizationId: string;
  currentSlideId: string;
  onSelectSlideById: (id: string) => void;
}) {
  const { sortable, ref } = useSortable({
    id: slide.id,
    index,
  });

  useEffect(() => {
    if (sortable.isDropping) {
      onSelectSlideById(slide.id);
    }
  }, [sortable.isDropping]);

  return (
    <div
      ref={ref}
      className={cn(
        "mb-2 cursor-pointer rounded-md border-2 p-1 transition-colors hover:bg-gray-100",
        currentSlideId === slide.id && "border-2 border-primary bg-primary/5",
      )}
      onClick={() => onSelectSlideById(slide.id)}
    >
      <div className="flex items-center justify-between gap-2">
        <div className="flex size-5 items-center justify-center rounded-full bg-gray-900 text-xs text-primary-foreground">
          {sortable.index + 1}
        </div>

        <SlideActionsMenu
          slide={slide}
          organizationId={organizationId}
          disabled={disabled}
        />
      </div>
      <div className="relative mt-2 h-32 w-full overflow-hidden rounded bg-muted">
        {slide.imageUrl ? (
          <Image
            src={slide.imageUrl}
            alt={""}
            fill
            sizes="600px"
            className="object-fill"
          />
        ) : (
          <div className="h-full w-full bg-gray-50" />
        )}
      </div>
    </div>
  );
}
