import { z } from "@/libs/zod";

export const LocationCreateSchema = z.object({
  name: z.string().min(1, "Location name is required"),
  address: z.string().min(1, "Address is required"),
  city: z.string().min(1, "City is required"),
  state: z.string().min(1, "State is required"),
  country: z.string().min(1, "Country is required"),
  postalCode: z.string().min(1, "Postal code is required"),
  organizationId: z.string(),
});

export const LocationUpdateSchema = LocationCreateSchema.partial().extend({
  id: z.string(),
});

export const SubLocationCreateSchema = z.object({
  name: z.string().min(1, "Sublocation name is required"),
  locationId: z.string(),
});

export const SubLocationUpdateSchema = SubLocationCreateSchema.partial().extend(
  {
    id: z.string(),
  },
);
