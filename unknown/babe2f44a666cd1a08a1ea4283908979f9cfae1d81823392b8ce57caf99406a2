import type { RouterInputs, RouterOutputs } from "@/trpc/react";
import type { InfiniteData } from "@tanstack/react-query";

export type ApprovalStatus = "pending" | "approved" | "rejected";

export type NotificationType = "approval_requested" | "approved" | "rejected";

export interface ApprovalFilter {
  status?: ApprovalStatus | "all";
  projectId?: string;
  cursor?: string;
  take?: number;
}

// Router Input Types
export type ApprovalSubmitInput =
  RouterInputs["approvals"]["submitForApproval"];

export type ApprovalReviewInput = RouterInputs["approvals"]["reviewApproval"];

export type ApprovalsGetAllInput = RouterInputs["approvals"]["getAll"];

export type ApprovalGetByIdInput = RouterInputs["approvals"]["getById"];

export type PendingReviewsInput =
  RouterInputs["approvals"]["getPendingReviews"];

// Router Output Types
export type ApprovalSubmitOutput =
  RouterOutputs["approvals"]["submitForApproval"];

export type ApprovalReviewOutput = RouterOutputs["approvals"]["reviewApproval"];

export type ApprovalsGetAllOutput = RouterOutputs["approvals"]["getAll"];

export type ApprovalGetByIdOutput = RouterOutputs["approvals"]["getById"];

export type PendingReviewsOutput =
  RouterOutputs["approvals"]["getPendingReviews"];

// Infinite Query Types
export type InfiniteApprovalsData = InfiniteData<ApprovalsGetAllOutput>;

// Individual approval items from the output arrays
export type ApprovalItem = ApprovalsGetAllOutput["data"][0];

export type PendingReviewItem = PendingReviewsOutput[0];
