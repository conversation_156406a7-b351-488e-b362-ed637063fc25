"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Loader } from "@/components/ui/loader";
import { useSetLocalStorage } from "@/hooks/use-local-storage";
import { APP_ROUTES } from "@/libs/constants";
import { useOrganizations } from "@/queries/organization.queries";
import { getInitials } from "@/utils/get-initials";
import { IconArrowRight } from "@tabler/icons-react";
import { useRouter } from "next/navigation";

export default function OrganizationsSelectView() {
  const router = useRouter();
  const setSelectedOrg = useSetLocalStorage();
  const { data, isLoading } = useOrganizations();

  const handleOrganizationSelect = (slug: string | null) => {
    if (!slug) return;
    setSelectedOrg("selectedOrg", slug);
    router.push(`/${slug}/dashboard`);
  };

  if (isLoading) {
    return (
      <div className="flex h-[calc(100vh-65px)] items-center justify-center">
        <Loader />
      </div>
    );
  }

  if (!data?.data?.length) {
    return (
      <div className="container py-8">
        <div className="mb-6 flex flex-col items-center justify-center space-y-2">
          <h1 className="text-2xl font-semibold">Select an Organization</h1>
        </div>

        <Card>
          <div className="flex flex-col items-center justify-center gap-6 p-10">
            <div className="flex flex-col items-center gap-2 text-center">
              <h2 className="text-xl font-semibold">
                Create your first organization
              </h2>
              <p className="text-muted-foreground">
                Organizations help you collaborate with your team and manage
                your projects.
              </p>
            </div>
            <Button
              onClick={() => router.push(APP_ROUTES.CREATE_ORGANIZATION)}
              size="lg"
            >
              Create organization
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="container py-8">
      <h1 className="mb-6 text-2xl font-semibold">Select an Organization</h1>
      <div className="grid gap-4">
        {data?.data?.map((org) => (
          <Card
            key={org.id}
            className="cursor-pointer"
            onClick={() => handleOrganizationSelect(org.slug)}
          >
            <div className="flex items-center justify-between p-6">
              <div className="flex flex-row items-center gap-4">
                <Avatar className="size-12 rounded-lg">
                  <AvatarImage src={org.logo ?? ""} alt={org.name} />
                  <AvatarFallback className="rounded-lg text-lg">
                    {getInitials(org.name, 1)}
                  </AvatarFallback>
                </Avatar>
                <div className="flex flex-col gap-1">
                  <h2 className="text-lg font-semibold">{org.name}</h2>
                  <Badge variant="gray" className="w-fit">
                    {org._count.members} member{org._count.members > 1 && "s"}
                  </Badge>
                </div>
              </div>

              <Button variant="ghost" size="icon">
                <IconArrowRight className="size-4" />
              </Button>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
}
