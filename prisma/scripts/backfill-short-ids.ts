import { PrismaClient } from "@prisma/client";
import { custom<PERSON><PERSON><PERSON><PERSON> } from "nanoid";

const prisma = new PrismaClient();

// 6-char, URL-safe alphabet
const nanoid = customAlphabet(
  "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",
  6,
);

async function backfillLocations() {
  const all = await prisma.location.findMany({
    where: { shortId: null },
    select: { id: true, organizationId: true },
  });

  for (const loc of all) {
    let code: string;
    let tries = 0;

    // avoid collisions
    do {
      if (++tries > 5)
        throw new Error(`Too many shortId collisions for Location ${loc.id}`);
      code = nanoid();
    } while (
      await prisma.location.findFirst({
        where: { organizationId: loc.organizationId, shortId: code },
      })
    );

    await prisma.location.update({
      where: { id: loc.id },
      data: { shortId: code },
    });
    console.log(`Location ${loc.id} → shortId=${code}`);
  }
}

async function backfillSubLocations() {
  const all = await prisma.subLocation.findMany({
    where: { shortId: null },
    select: { id: true, locationId: true },
  });

  for (const sl of all) {
    let code: string;
    let tries = 0;

    do {
      if (++tries > 5)
        throw new Error(`Too many shortId collisions for SubLocation ${sl.id}`);
      code = nanoid();
    } while (
      await prisma.subLocation.findFirst({
        where: { locationId: sl.locationId, shortId: code },
      })
    );

    await prisma.subLocation.update({
      where: { id: sl.id },
      data: { shortId: code },
    });
    console.log(`SubLocation ${sl.id} → shortId=${code}`);
  }
}

async function main() {
  console.log("Backfilling Location.shortId…");
  await backfillLocations();
  console.log("Backfilling SubLocation.shortId…");
  await backfillSubLocations();
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(() => prisma.$disconnect());
