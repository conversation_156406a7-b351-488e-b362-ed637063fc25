SELECT 
  pa.id,
  pa.projectId,
  pa.requesterId,
  pa.approverId,
  pa.status,
  pa.submittedAt,
  pa.reviewedAt,
  pa.comments,
  pa.reviewComments,
  pa.createdAt,
  pa.updatedAt,
  p.id as project_id,
  p.name as project_name,
  p.status as project_status,
  (SELECT COUNT(*) FROM slides s WHERE s.projectId = p.id AND s.deletedAt IS NULL) as slide_count,
  req.id as requester_id,
  req.name as requester_name,
  req.email as requester_email,
  req.image as requester_image,
  app.id as approver_id,
  app.name as approver_name,
  app.email as approver_email,
  app.image as approver_image
FROM project_approvals pa
INNER JOIN projects p ON pa.projectId = p.id AND p.deletedAt IS NULL
INNER JOIN user req ON pa.requesterId = req.id AND req.deletedAt IS NULL
LEFT JOIN user app ON pa.approverId = app.id AND app.deletedAt IS NULL
INNER JOIN (
  SELECT 
    projectId,
    MAX(createdAt) as max_created
  FROM project_approvals
  WHERE deletedAt IS NULL
  GROUP BY projectId
) latest ON pa.projectId = latest.projectId AND pa.createdAt = latest.max_created
WHERE pa.deletedAt IS NULL
  AND p.deletedAt IS NULL
  AND p.organizationId = ?
  AND (
    (p.status = 'pending_approval' AND pa.status = 'pending') OR
    (p.status = 'rejected' AND pa.status = 'rejected') OR
    (p.status = 'approved' AND pa.status = 'approved')
  )
  AND (? IS NULL OR ? = '' OR p.name LIKE CONCAT('%', ?, '%'))
  AND (? IS NULL OR ? = 'all' OR pa.status = ?)
  AND (? IS NULL OR pa.projectId = ?)
  AND (? IS NULL OR pa.requesterId = ?)
  AND (? IS NULL OR pa.createdAt < (SELECT createdAt FROM project_approvals WHERE id = ?))
ORDER BY pa.createdAt DESC
LIMIT ?;