-- DropForeignKey
ALTER TABLE `_ProjectLocations` DROP FOREIGN KEY `_ProjectLocations_A_fkey`;

-- DropForeignKey
ALTER TABLE `_ProjectLocations` DROP FOREIGN KEY `_ProjectLocations_B_fkey`;

-- DropForeignKey
ALTER TABLE `_ProjectSublocations` DROP FOREIGN KEY `_ProjectSublocations_A_fkey`;

-- DropForeignKey
ALTER TABLE `_ProjectSublocations` DROP FOREIGN KEY `_ProjectSublocations_B_fkey`;

-- DropForeignKey
ALTER TABLE `account` DROP FOREIGN KEY `account_userId_fkey`;

-- DropForeignKey
ALTER TABLE `approval_notifications` DROP FOREIGN KEY `approval_notifications_approvalId_fkey`;

-- DropForeignKey
ALTER TABLE `approval_notifications` DROP FOREIGN KEY `approval_notifications_userId_fkey`;

-- DropForeignKey
ALTER TABLE `invitations` DROP FOREIGN KEY `invitations_inviterId_fkey`;

-- DropForeignKey
ALTER TABLE `invitations` DROP FOREIGN KEY `invitations_organizationId_fkey`;

-- DropForeignKey
ALTER TABLE `locations` DROP FOREIGN KEY `locations_organizationId_fkey`;

-- DropForeignKey
ALTER TABLE `members` DROP FOREIGN KEY `members_organizationId_fkey`;

-- DropForeignKey
ALTER TABLE `members` DROP FOREIGN KEY `members_userId_fkey`;

-- DropForeignKey
ALTER TABLE `passkey` DROP FOREIGN KEY `passkey_userId_fkey`;

-- DropForeignKey
ALTER TABLE `project_approvals` DROP FOREIGN KEY `project_approvals_approverId_fkey`;

-- DropForeignKey
ALTER TABLE `project_approvals` DROP FOREIGN KEY `project_approvals_projectId_fkey`;

-- DropForeignKey
ALTER TABLE `project_approvals` DROP FOREIGN KEY `project_approvals_requesterId_fkey`;

-- DropForeignKey
ALTER TABLE `projects` DROP FOREIGN KEY `projects_creatorId_fkey`;

-- DropForeignKey
ALTER TABLE `projects` DROP FOREIGN KEY `projects_organizationId_fkey`;

-- DropForeignKey
ALTER TABLE `session` DROP FOREIGN KEY `session_userId_fkey`;

-- DropForeignKey
ALTER TABLE `slides` DROP FOREIGN KEY `slides_projectId_fkey`;

-- DropForeignKey
ALTER TABLE `sublocations` DROP FOREIGN KEY `sublocations_locationId_fkey`;

-- DropIndex
DROP INDEX `projects_creatorId_fkey` ON `projects`;

-- DropIndex
DROP INDEX `projects_organizationId_fkey` ON `projects`;

-- AlterTable
ALTER TABLE `account` ADD COLUMN `deletedAt` DATETIME(3) NULL;

-- AlterTable
ALTER TABLE `approval_notifications` ADD COLUMN `deletedAt` DATETIME(3) NULL;

-- AlterTable
ALTER TABLE `invitations` ADD COLUMN `deletedAt` DATETIME(3) NULL;

-- AlterTable
ALTER TABLE `locations` ADD COLUMN `deletedAt` DATETIME(3) NULL;

-- AlterTable
ALTER TABLE `members` ADD COLUMN `deletedAt` DATETIME(3) NULL;

-- AlterTable
ALTER TABLE `organizations` ADD COLUMN `deletedAt` DATETIME(3) NULL;

-- AlterTable
ALTER TABLE `passkey` ADD COLUMN `deletedAt` DATETIME(3) NULL;

-- AlterTable
ALTER TABLE `project_approvals` ADD COLUMN `deletedAt` DATETIME(3) NULL;

-- AlterTable
ALTER TABLE `projects` ADD COLUMN `deletedAt` DATETIME(3) NULL;

-- AlterTable
ALTER TABLE `session` ADD COLUMN `deletedAt` DATETIME(3) NULL;

-- AlterTable
ALTER TABLE `slides` ADD COLUMN `deletedAt` DATETIME(3) NULL;

-- AlterTable
ALTER TABLE `sublocations` ADD COLUMN `deletedAt` DATETIME(3) NULL;

-- AlterTable
ALTER TABLE `subscriptions` ADD COLUMN `deletedAt` DATETIME(3) NULL;

-- AlterTable
ALTER TABLE `user` ADD COLUMN `deletedAt` DATETIME(3) NULL;

-- AlterTable
ALTER TABLE `verification` ADD COLUMN `deletedAt` DATETIME(3) NULL;

-- CreateIndex
CREATE INDEX `account_deletedAt_idx` ON `account`(`deletedAt`);

-- CreateIndex
CREATE INDEX `approval_notifications_deletedAt_idx` ON `approval_notifications`(`deletedAt`);

-- CreateIndex
CREATE INDEX `invitations_deletedAt_idx` ON `invitations`(`deletedAt`);

-- CreateIndex
CREATE INDEX `locations_deletedAt_idx` ON `locations`(`deletedAt`);

-- CreateIndex
CREATE INDEX `members_deletedAt_idx` ON `members`(`deletedAt`);

-- CreateIndex
CREATE INDEX `organizations_deletedAt_idx` ON `organizations`(`deletedAt`);

-- CreateIndex
CREATE INDEX `passkey_deletedAt_idx` ON `passkey`(`deletedAt`);

-- CreateIndex
CREATE INDEX `project_approvals_deletedAt_idx` ON `project_approvals`(`deletedAt`);

-- CreateIndex
CREATE INDEX `projects_deletedAt_idx` ON `projects`(`deletedAt`);

-- CreateIndex
CREATE INDEX `session_deletedAt_idx` ON `session`(`deletedAt`);

-- CreateIndex
CREATE INDEX `slides_deletedAt_idx` ON `slides`(`deletedAt`);

-- CreateIndex
CREATE INDEX `sublocations_deletedAt_idx` ON `sublocations`(`deletedAt`);

-- CreateIndex
CREATE INDEX `subscriptions_deletedAt_idx` ON `subscriptions`(`deletedAt`);

-- CreateIndex
CREATE INDEX `user_deletedAt_idx` ON `user`(`deletedAt`);

-- CreateIndex
CREATE INDEX `verification_deletedAt_idx` ON `verification`(`deletedAt`);
