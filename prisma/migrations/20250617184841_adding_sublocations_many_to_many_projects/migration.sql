-- CreateTable
CREATE TABLE `_ProjectSublocations` (
    `A` VARCHAR(191) NOT NULL,
    `B` VARCHAR(191) NOT NULL,

    UNIQUE INDEX `_ProjectSublocations_AB_unique`(`A`, `B`),
    INDEX `_ProjectSublocations_B_index`(`B`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `_ProjectSublocations` ADD CONSTRAINT `_ProjectSublocations_A_fkey` FOREIGN KEY (`A`) REFERENCES `projects`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- Add<PERSON><PERSON><PERSON>K<PERSON>
ALTER TABLE `_ProjectSublocations` ADD CONSTRAINT `_ProjectSublocations_B_fkey` FOREIGN KEY (`B`) REFERENCES `sublocations`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
