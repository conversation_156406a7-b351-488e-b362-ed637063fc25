# Since the ".env" file is gitignored, you can use the ".env.example" file to
# build a new ".env" file when you clone the repo. Keep this file up-to-date
# when you add new variables to `.env`.

# This file will be committed to version control, so make sure not to have any
# secrets in it. If you are cloning this repo, create a copy of this file named
# ".env" and populate it with your secrets.

# When adding additional environment variables, the schema in "/src/env.js"
# should be updated accordingly.

# App
APP_BASE_URL="http://localhost:3000"
NEXT_PUBLIC_APP_BASE_URL="http://localhost:3000"

# Prisma
# https://www.prisma.io/docs/reference/database-reference/connection-urls#env
DATABASE_URL="postgresql://postgres:password@localhost:5432/saas-template-v2"

# Better Auth
# You can generate a new secret on the command line with:
# npx @better-auth/cli@latest secret
# https://www.better-auth.com/docs/concepts/cli#secret
BETTER_AUTH_SECRET="49puGPA8S1PY4He4/JNxxRgBssWg1qJkLYA4orvtFD4=" # Generated by create-t3-app.
BETTER_AUTH_URL="http://localhost:3000" #Base URL of your app

# Google Auth
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""

# Github Auth
GITHUB_CLIENT_ID=""
GITHUB_CLIENT_SECRET=""