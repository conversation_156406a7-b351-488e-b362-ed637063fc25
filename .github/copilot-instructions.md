# SlydeShow AI Coding Agent Instructions

## Project Overview

SlydeShow is a **T3 Stack Next.js application** for managing digital slideshow projects within organizations. Users create organizations, upload slides, manage projects through approval workflows, and display slideshows at physical locations.

## Code Style

- Use TypeScript whenever possible.
- Use kebab-case for all file names. Avoid capital letters.
- Use '@/ path aliases for imports.
- Use root src directory.

## Core Architecture

### Tech Stack

- **Next.js 15** with App Router (`src/app/`)
- **tRPC** for type-safe API routes (`src/server/api/routers/`)
- **Prisma** with MySQL/PlanetScale database
- **Better Auth** for authentication with organization support
- **Tailwind CSS** with shadcn/ui components
- **Tigris Data** (S3-compatible) for file storage
- **Stripe** for payments

### Database Schema Patterns

- **Soft deletes**: All main entities have `deletedAt` fields
- **Multi-tenant**: Organization-scoped data with `organizationId` fields
- **Fractional indexing**: Slides use string-based `order` field for drag-and-drop reordering
- **Approval system**: Projects have status workflow (`editing` → `pending_approval` → `approved` → `active` → `completed`)

### Authentication & Authorization

- **Better Auth** with organization plugin handles multi-tenant auth
- **Role-based permissions**: `admin` (full access), `manager` (limited admin), `member` (view only)
- **Session management**: Active organization stored in session via `activeOrganizationId`
- **Permission patterns**: Use `usePermissions()` hook and `<PermissionGuard>` component

## Key Development Patterns

### tRPC Structure

```typescript
// All API routes in src/server/api/routers/
// Pattern: entity.router.ts exports entityRouter
// Use protectedProcedure for authenticated endpoints
// Input validation with Zod schemas from src/schemas/
```

### File Upload System

- **Better Upload** library with pre-signed URLs
- **Organization-scoped storage**: `organizations/{orgId}/{filename}`
- **Image processing**: Automatic resizing and optimization
- **Cleanup on delete**: Always delete S3 objects when removing database records

### Project Workflow

1. **Creation**: Projects start in `editing` status
2. **Slide management**: Drag-and-drop reordering with fractional indexing
3. **Approval**: Submit for approval → admin reviews → approved/rejected
4. **Deployment**: Active projects show at assigned locations
5. **Lifecycle**: Auto-complete when past end date

### Slide Management

- **Fractional indexing**: Use `generateKeyBetween()` for drag-and-drop ordering
- **Batch operations**: Use `slides.batchUpdate` for reordering multiple slides
- **Image uploads**: Via `multipleSlideUpload` and `singleSlideUpload` routes
- **S3 cleanup**: Always call `deleteFile()` when removing slides

## Development Workflows

### Database Changes

```bash
# Update schema
npx prisma db push
# Generate client with SQL support
npx prisma generate --sql
# Run migrations in production
npx prisma migrate deploy
```

### File Upload Integration

```typescript
// Always follow this pattern for file uploads:
1. Get pre-signed URL via tRPC storage.getUploadUrl
2. Upload to S3 using Better Upload
3. Save file metadata to database
4. Delete old files when updating
```

### Component Patterns

- **Query hooks**: Use `src/queries/` for tRPC query abstractions
- **Form handling**: React Hook Form with Zod validation
- **Error handling**: Consistent error boundaries and toast notifications
- **Loading states**: Use `isPending` from tRPC mutations

## Critical Integration Points

### Organization Context

- Always check current organization via `useOrganizationSlug()`
- Filter all data queries by `organizationId`
- Handle organization switching in middleware

### Permission Checks

```typescript
// Use permission hooks, not direct role checks
const { canEditProject, isAdmin } = usePermissions();
// Or use PermissionGuard component
<PermissionGuard allowedRoles={['admin', 'manager']}>
```

### Location Management

- Projects can be assigned to multiple locations and sublocations
- Location-based slideshow display via `getLocationSlides()`
- Hierarchy: Organization → Location → SubLocation

## Environment & Configuration

### Required Environment Variables

- **Database**: `DATABASE_URL` (MySQL/PlanetScale)
- **Auth**: `BETTER_AUTH_SECRET`, `BETTER_AUTH_URL`
- **Storage**: `S3_BUCKET_NAME`, `S3_ACCESS_KEY_ID`, `S3_SECRET_ACCESS_KEY`
- **OAuth**: `GOOGLE_CLIENT_ID`, `GITHUB_CLIENT_ID` (with secrets)
- **Email**: `RESEND_API_KEY`
- **Payments**: `STRIPE_SECRET_KEY`, `STRIPE_WEBHOOK_SECRET`

### Development Commands

```bash
pnpm dev              # Start dev server
pnpm db:studio        # Open Prisma Studio
pnpm db:push          # Push schema changes
pnpm lint             # ESLint + TypeScript check
pnpm format:write     # Prettier formatting
```

## Anti-Patterns to Avoid

❌ **Don't** query across organizations without proper filtering
❌ **Don't** use numeric IDs for slide ordering (use fractional indexing)
❌ **Don't** delete files from S3 without removing database records
❌ **Don't** hardcode role names (use `Roles` enum from types)
❌ **Don't** bypass tRPC for database operations (use server actions sparingly)

## Testing & Debugging

### Database Debugging

- Use `src/server/scripts/normalize-slide-orders.ts` for slide order fixes
- Check `src/prisma/sql/` for raw SQL queries
- Monitor soft deletes via `deletedAt` fields

### File Upload Debugging

- Verify S3 bucket permissions and CORS configuration
- Check file naming patterns: `{organizationId}-{originalName}`
- Use `better-upload` debug logs for upload failures

When working on this codebase, always consider the organization context, permission model, and proper cleanup of external resources.
