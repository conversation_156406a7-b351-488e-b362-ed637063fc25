# SlydeShow User Guide

SlydeShow is a comprehensive digital slideshow management platform designed for organizations to create, manage, and deploy slideshow presentations across multiple physical locations. This guide will walk you through every aspect of using SlydeShow effectively.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Creating Your Organization](#creating-your-organization)
3. [Understanding User Roles](#understanding-user-roles)
4. [Managing Team Members](#managing-team-members)
5. [Setting Up Locations](#setting-up-locations)
6. [Creating Projects](#creating-projects)
7. [Managing Slides](#managing-slides)
8. [Project Approval Workflow](#project-approval-workflow)
9. [Publishing Projects](#publishing-projects)
10. [Accessing Deployed Slideshows](#accessing-deployed-slideshows)
11. [Monitoring and Analytics](#monitoring-and-analytics)

---

## Getting Started

### Account Registration

1. **Sign Up**: Visit the SlydeShow platform and create your account using:
   - Email and password
   - Google OAuth
   - GitHub OAuth

2. **Account Setup**: Complete your profile with:
   - First name and last name
   - Profile picture (automatically generated avatar)

_[Image placeholder: Registration form screenshot]_

### Initial Setup

After registration, you'll be redirected to create your first organization, which is required to start using SlydeShow.

---

## Creating Your Organization

Organizations are the top-level containers for all your projects, locations, and team members.

### Organization Creation Process

1. **Access Creation Page**: After signup, you'll be automatically directed to the organization creation page
2. **Fill Organization Details**:
   - **Organization Name**: Enter your company or organization name
   - **Organization Slug**: A URL-friendly identifier (auto-generated from name, but editable)
   - **Logo**: Automatically generated based on organization name

_[Image placeholder: Organization creation form]_

### Organization Settings

Once created, you can manage:

- Organization profile and branding
- Billing and subscription settings
- General settings and preferences

---

## Understanding User Roles

SlydeShow uses a role-based permission system with three distinct levels:

### Admin Role

- **Full access** to all organization features
- Can manage team members and their roles
- Can approve/reject project submissions
- Can publish projects directly to locations
- Can manage organization settings and billing

### Manager Role

- Can create and edit projects
- Can manage slides and project content
- Can submit projects for approval
- **Cannot** publish projects directly
- **Cannot** manage team members

### Member Role

- Can create and edit projects
- Can manage slides and project content
- Can submit projects for approval
- **Cannot** publish projects directly
- **Cannot** manage team members
- Limited access to organization features

_[Image placeholder: Role permissions comparison table]_

---

## Managing Team Members

### Inviting Team Members

**Admin-only feature**

1. **Navigate to Team Settings**: Go to your organization settings
2. **Send Invitations**:
   - Enter team member's email address
   - Select their role (Admin, Manager, or Member)
   - Send invitation

3. **Invitation Process**:
   - Invitee receives email with invitation link
   - They create account or sign in
   - Automatically added to organization with assigned role

_[Image placeholder: Team invitation interface]_

### Managing Existing Members

- **View team roster** with roles and status
- **Update member roles** (Admin only)
- **Remove team members** (Admin only)
- **Resend invitations** for pending invites

---

## Setting Up Locations

Locations represent physical spaces where your slideshows will be displayed. SlydeShow supports a hierarchical location structure.

### Location Hierarchy

```
Organization
├── Location (e.g., "Downtown Office")
│   ├── Sublocation (e.g., "Lobby Display")
│   ├── Sublocation (e.g., "Conference Room A")
│   └── Sublocation (e.g., "Break Room")
└── Location (e.g., "Warehouse Facility")
    ├── Sublocation (e.g., "Main Entrance")
    └── Sublocation (e.g., "Loading Dock")
```

### Creating Locations

1. **Navigate to Locations**: Access the locations section in your dashboard
2. **Add New Location**:
   - **Location Name**: Descriptive name (e.g., "Downtown Office")
   - **Address Information**: Complete address details
   - **State/Region**: For organizational purposes

_[Image placeholder: Location creation form]_

### Adding Sublocations

1. **Select Parent Location**: Choose the main location
2. **Create Sublocation**:
   - **Sublocation Name**: Specific area name (e.g., "Lobby Display")
   - **Description**: Optional details about the display area

_[Image placeholder: Sublocation management interface]_

### Location Management Features

- **Edit location details** and address information
- **Manage sublocations** within each location
- **Generate deployment URLs** for each location/sublocation
- **View active projects** assigned to each location

---

## Creating Projects

Projects are containers for your slideshow content and represent individual presentation campaigns.

### Project Creation Process

1. **Access Projects Dashboard**: Navigate to the projects section
2. **Create New Project**:
   - Click "Create Project" button
   - Enter **Project Name** (descriptive and meaningful)
   - Project starts in "Editing" status

_[Image placeholder: Project creation dialog]_

### Project Statuses

Projects progress through several statuses:

- **Editing**: Initial creation state, content can be modified
- **Pending Approval**: Submitted for admin review
- **Approved**: Admin-approved, ready for publishing
- **Active**: Currently displaying at assigned locations
- **Completed**: Past end date, no longer displaying
- **Rejected**: Admin-rejected, returned to editing

_[Image placeholder: Project status workflow diagram]_

### Project Settings

Configure project-level settings:

- **Project Name**: Update anytime during editing
- **Slide Duration**: Default duration for all slides (can be overridden per slide)
- **Project Description**: Optional project details

---

## Managing Slides

Slides are the individual images that make up your slideshow presentation.

### Adding Slides

1. **Open Project**: Navigate to your project
2. **Upload Slides**:
   - Click "Add Slides" button
   - Select multiple image files (JPEG, PNG, WebP)
   - Images are automatically optimized and uploaded

_[Image placeholder: Slide upload interface]_

### Slide Management Features

#### Drag-and-Drop Reordering

- **Reorder slides** by dragging them in the sidebar
- **Real-time preview** of slide order
- **Intuitive interface** for easy slide organization

#### Individual Slide Settings

- **Custom Duration**: Override project default for specific slides
- **Slide Preview**: Full-size preview of each slide
- **Delete Slides**: Remove unwanted slides

_[Image placeholder: Slide management interface with drag-and-drop]_

#### Slide Optimization

- **Automatic Processing**: Images are automatically optimized for best display quality
- **Secure Storage**: All slides are safely stored in the cloud

### Slide Display Features

- **Smooth Transitions**: Seamless fade between slides
- **Responsive Design**: Adapts to different screen sizes
- **Seamless Playback**: Optimized for smooth slideshow experience

---

## Project Approval Workflow

SlydeShow implements a structured approval process to ensure content quality and compliance.

### Submission Process (Manager/Member)

1. **Complete Project**: Ensure all slides are uploaded and configured
2. **Submit for Approval**:
   - Click "Submit for Approval" button
   - Select target locations and sublocations
   - Set start and end dates
   - Add optional comments for reviewers
   - Project status changes to "Pending Approval"

_[Image placeholder: Approval submission form]_

### Review Process (Admin)

1. **Access Approval Queue**: Navigate to approvals dashboard
2. **Review Submission**:
   - Preview all slides in the project
   - Review proposed locations and dates
   - Read submitter comments

3. **Make Decision**:
   - **Approve**: Project becomes "Approved" and ready for publishing
   - **Reject**: Project returns to "Editing" with feedback comments

_[Image placeholder: Approval review interface]_

### Approval Notifications

- **Email notifications** sent to relevant parties
- **In-app notifications** for status changes
- **Audit trail** of all approval decisions

---

## Publishing Projects

Publishing makes your approved projects live at designated locations.

### Direct Publishing (Admin Only)

1. **Open Approved Project**: Navigate to an approved project
2. **Click "Publish Project"**:
   - **Select Locations**: Choose target locations and sublocations
   - **Set Schedule**: Define start and end dates
   - **Confirm Publishing**: Project becomes "Active"

_[Image placeholder: Publishing interface with location selection]_

### Publishing via Approval (Manager/Member)

For non-admin users, publishing happens through the approval process:

1. Submit project with locations and dates
2. Admin reviews and approves
3. Project automatically becomes active on start date

### Scheduling Features

- **Start Date**: When slideshow begins displaying
- **End Date**: When slideshow stops displaying
- **Automatic Status Updates**: Projects automatically transition to "Completed" after end date
- **Multiple Locations**: Single project can display at multiple locations simultaneously

---

## Accessing Deployed Slideshows

Once published, slideshows are accessible via unique URLs for each location.

### Deployment URLs

Each location and sublocation has a unique URL format:

```
https://your-domain.com/view?location=LOCATION_ID&sub-location=SUBLOCATION_ID
```

### URL Generation

1. **Navigate to Locations**: Access your locations dashboard
2. **Copy Deployment URL**: Each location/sublocation has a "Copy URL" button
3. **Share URL**: Provide URL to display device or kiosk

_[Image placeholder: Location URL copying interface]_

### Display Modes

#### Standard Mode

- **Interactive Navigation**: Users can navigate slides manually
- **Auto-advance**: Slides advance automatically based on duration
- **Browser Controls**: Standard browser interface available

#### Kiosk Mode

- **Full-screen Display**: Optimized for dedicated display devices
- **Locked Interface**: Prevents accidental navigation or interruption
- **Always-on Display**: Keeps screen active during presentation
- **Reliable Connection**: Maintains stable display even with network interruptions

_[Image placeholder: Slideshow display in kiosk mode]_

### Real-time Updates

- **Automatic Updates**: Slideshows refresh periodically with new content
- **Content Sync**: New slides appear automatically
- **Live Changes**: Updates appear without manual intervention

---

## Monitoring and Analytics

### Project Activity Dashboard

Track your organization's slideshow activity:

- **Active Projects**: Currently displaying projects
- **Project Statistics**: Total projects, completion rates
- **Recent Activity**: Latest project updates and changes
- **Team Activity**: Member contributions and activity

_[Image placeholder: Analytics dashboard]_

### Location Monitoring

- **Deployment Status**: Which projects are active at each location
- **Display URLs**: Quick access to all deployment links
- **Location Usage**: Track which locations are most active

### Project Performance

- **Display Duration**: How long projects have been active
- **Location Coverage**: Which locations are displaying content
- **Team Collaboration**: Track project contributors

---

## Best Practices

### Content Creation

- **High-Quality Images**: Use high-resolution images for best display quality
- **Consistent Branding**: Maintain visual consistency across slides
- **Appropriate Duration**: Set slide durations based on content complexity
- **Test Before Publishing**: Preview projects before submission

### Organization Management

- **Clear Role Assignment**: Assign appropriate roles based on responsibilities
- **Regular Content Updates**: Keep slideshow content fresh and relevant
- **Location Organization**: Use descriptive names for locations and sublocations
- **Approval Workflow**: Establish clear approval criteria and processes

### Technical Considerations

- **Internet Connection**: Ensure reliable internet at display locations
- **Display Setup**: Use appropriate screens and devices for your needs
- **Regular Monitoring**: Check that slideshows are displaying correctly

---

## Troubleshooting

### Common Issues

**Slides Not Displaying**

- Verify project is in "Active" status
- Check start/end dates are current
- Confirm correct location URL
- Ensure stable internet connection

**Upload Issues**

- Check image file formats (common formats supported) (JPEG, PNG, WebP)
- Verify file sizes are reasonable
- Ensure stable internet connection during upload

**Permission Errors**

- Verify user role and permissions
- Contact organization admin for role updates
- Check organization membership status

### Support Resources

- **Email Support**: Contact support team for technical issues - <EMAIL>

---

_This guide covers the complete SlydeShow workflow from initial setup to deployment. For additional support or advanced features, <NAME_EMAIL>._
